  ic_media_pause android.R.drawable  
ic_media_play android.R.drawable  ic_menu_search android.R.drawable  AccessibilityService android.accessibilityservice  AccessibilityServiceInfo android.accessibilityservice  GestureDescription android.accessibilityservice  AccessibilityEvent 1android.accessibilityservice.AccessibilityService  AccessibilityNodeInfo 1android.accessibilityservice.AccessibilityService  Bundle 1android.accessibilityservice.AccessibilityService  ChatMessage 1android.accessibilityservice.AccessibilityService  ConcurrentHashMap 1android.accessibilityservice.AccessibilityService  CoroutineScope 1android.accessibilityservice.AccessibilityService  CustomerServiceListHandler 1android.accessibilityservice.AccessibilityService  Dispatchers 1android.accessibilityservice.AccessibilityService  	Exception 1android.accessibilityservice.AccessibilityService  GLOBAL_ACTION_BACK 1android.accessibilityservice.AccessibilityService  GestureResultCallback 1android.accessibilityservice.AccessibilityService  Handler 1android.accessibilityservice.AccessibilityService  Log 1android.accessibilityservice.AccessibilityService  Looper 1android.accessibilityservice.AccessibilityService  MessageGroup 1android.accessibilityservice.AccessibilityService  Rect 1android.accessibilityservice.AccessibilityService  Regex 1android.accessibilityservice.AccessibilityService  Runnable 1android.accessibilityservice.AccessibilityService  String 1android.accessibilityservice.AccessibilityService  
SupervisorJob 1android.accessibilityservice.AccessibilityService  TAG 1android.accessibilityservice.AccessibilityService  any 1android.accessibilityservice.AccessibilityService  apply 1android.accessibilityservice.AccessibilityService  cancel 1android.accessibilityservice.AccessibilityService  chatHistoryCallback 1android.accessibilityservice.AccessibilityService  contains 1android.accessibilityservice.AccessibilityService  delay 1android.accessibilityservice.AccessibilityService  dispatchGesture 1android.accessibilityservice.AccessibilityService  extractChatHistory 1android.accessibilityservice.AccessibilityService  findNodeByClassName 1android.accessibilityservice.AccessibilityService  findNodeByText 1android.accessibilityservice.AccessibilityService  findScrollableNode 1android.accessibilityservice.AccessibilityService  handleCustomerServicePage 1android.accessibilityservice.AccessibilityService  instance 1android.accessibilityservice.AccessibilityService  isBlank 1android.accessibilityservice.AccessibilityService  isExtractionRunning 1android.accessibilityservice.AccessibilityService  launch 1android.accessibilityservice.AccessibilityService  listOf 1android.accessibilityservice.AccessibilityService  matches 1android.accessibilityservice.AccessibilityService  
mutableListOf 1android.accessibilityservice.AccessibilityService  	onDestroy 1android.accessibilityservice.AccessibilityService  onServiceConnected 1android.accessibilityservice.AccessibilityService  performGlobalAction 1android.accessibilityservice.AccessibilityService  	resources 1android.accessibilityservice.AccessibilityService  rootInActiveWindow 1android.accessibilityservice.AccessibilityService  serviceInfo 1android.accessibilityservice.AccessibilityService  set 1android.accessibilityservice.AccessibilityService  sortedBy 1android.accessibilityservice.AccessibilityService  
startsWith 1android.accessibilityservice.AccessibilityService  until 1android.accessibilityservice.AccessibilityService  withContext 1android.accessibilityservice.AccessibilityService  Log Gandroid.accessibilityservice.AccessibilityService.GestureResultCallback  TAG Gandroid.accessibilityservice.AccessibilityService.GestureResultCallback  FEEDBACK_ALL_MASK 5android.accessibilityservice.AccessibilityServiceInfo  flags 5android.accessibilityservice.AccessibilityServiceInfo  id 5android.accessibilityservice.AccessibilityServiceInfo  Builder /android.accessibilityservice.GestureDescription  StrokeDescription /android.accessibilityservice.GestureDescription  	addStroke 7android.accessibilityservice.GestureDescription.Builder  build 7android.accessibilityservice.GestureDescription.Builder  Activity android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  Service android.app  AccessibilityManager android.app.Activity  AccessibilityServiceInfo android.app.Activity  ActivityResultContracts android.app.Activity  Build android.app.Activity  ChatbotTheme android.app.Activity  Context android.app.Activity  FloatingWindowService android.app.Activity  Intent android.app.Activity  MainContent android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  Settings android.app.Activity  Toast android.app.Activity  Uri android.app.Activity  contains android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  getSystemService android.app.Activity  java android.app.Activity  onCreate android.app.Activity  onResume android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  
startActivity android.app.Activity  apply android.app.NotificationChannel  description android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  AccessibilityEvent android.app.Service  AccessibilityNodeInfo android.app.Service  Build android.app.Service  Bundle android.app.Service  
CHANNEL_ID android.app.Service  ChatMessage android.app.Service  ChatbotAccessibilityService android.app.Service  ConcurrentHashMap android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  CustomerServiceListHandler android.app.Service  Dispatchers android.app.Service  	Exception android.app.Service  GLOBAL_ACTION_BACK android.app.Service  Gravity android.app.Service  Handler android.app.Service  IllegalArgumentException android.app.Service  ImageButton android.app.Service  LayoutInflater android.app.Service  Log android.app.Service  Looper android.app.Service  Math android.app.Service  MessageGroup android.app.Service  MotionEvent android.app.Service  NOTIFICATION_ID android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  PixelFormat android.app.Service  R android.app.Service  Rect android.app.Service  Regex android.app.Service  Runnable android.app.Service  START_NOT_STICKY android.app.Service  START_STICKY android.app.Service  String android.app.Service  
SupervisorJob android.app.Service  TAG android.app.Service  Toast android.app.Service  
WindowManager android.app.Service  android android.app.Service  any android.app.Service  apply android.app.Service  cancel android.app.Service  chatHistoryCallback android.app.Service  contains android.app.Service  delay android.app.Service  extractChatHistory android.app.Service  findNodeByClassName android.app.Service  findNodeByText android.app.Service  findScrollableNode android.app.Service  getInstance android.app.Service  handleCustomerServicePage android.app.Service  instance android.app.Service  isBlank android.app.Service  isExtractionRunning android.app.Service  
isInitialized android.app.Service  java android.app.Service  launch android.app.Service  listOf android.app.Service  matches android.app.Service  
mutableListOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  	resources android.app.Service  rootInActiveWindow android.app.Service  set android.app.Service  sortedBy android.app.Service  startForeground android.app.Service  
startsWith android.app.Service  stopSelf android.app.Service  until android.app.Service  withContext android.app.Service  
ComponentName android.content  Context android.content  Intent android.content  ACCESSIBILITY_SERVICE android.content.Context  AccessibilityEvent android.content.Context  AccessibilityManager android.content.Context  AccessibilityNodeInfo android.content.Context  AccessibilityServiceInfo android.content.Context  ActivityResultContracts android.content.Context  Build android.content.Context  Bundle android.content.Context  
CHANNEL_ID android.content.Context  ChatMessage android.content.Context  ChatbotAccessibilityService android.content.Context  ChatbotTheme android.content.Context  ConcurrentHashMap android.content.Context  Context android.content.Context  CoroutineScope android.content.Context  CustomerServiceListHandler android.content.Context  Dispatchers android.content.Context  	Exception android.content.Context  FloatingWindowService android.content.Context  GLOBAL_ACTION_BACK android.content.Context  Gravity android.content.Context  Handler android.content.Context  IllegalArgumentException android.content.Context  ImageButton android.content.Context  Intent android.content.Context  LayoutInflater android.content.Context  Log android.content.Context  Looper android.content.Context  MainContent android.content.Context  Math android.content.Context  MessageGroup android.content.Context  Modifier android.content.Context  MotionEvent android.content.Context  NOTIFICATION_ID android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  PixelFormat android.content.Context  R android.content.Context  Rect android.content.Context  Regex android.content.Context  Runnable android.content.Context  START_NOT_STICKY android.content.Context  START_STICKY android.content.Context  Scaffold android.content.Context  Settings android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  TAG android.content.Context  Toast android.content.Context  Uri android.content.Context  WINDOW_SERVICE android.content.Context  
WindowManager android.content.Context  android android.content.Context  any android.content.Context  apply android.content.Context  cancel android.content.Context  chatHistoryCallback android.content.Context  contains android.content.Context  delay android.content.Context  enableEdgeToEdge android.content.Context  extractChatHistory android.content.Context  fillMaxSize android.content.Context  findNodeByClassName android.content.Context  findNodeByText android.content.Context  findScrollableNode android.content.Context  getInstance android.content.Context  getSystemService android.content.Context  handleCustomerServicePage android.content.Context  instance android.content.Context  isBlank android.content.Context  isExtractionRunning android.content.Context  
isInitialized android.content.Context  java android.content.Context  launch android.content.Context  listOf android.content.Context  matches android.content.Context  
mutableListOf android.content.Context  padding android.content.Context  	resources android.content.Context  rootInActiveWindow android.content.Context  set android.content.Context  
setContent android.content.Context  sortedBy android.content.Context  
startsWith android.content.Context  until android.content.Context  withContext android.content.Context  AccessibilityEvent android.content.ContextWrapper  AccessibilityManager android.content.ContextWrapper  AccessibilityNodeInfo android.content.ContextWrapper  AccessibilityServiceInfo android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  ChatMessage android.content.ContextWrapper  ChatbotAccessibilityService android.content.ContextWrapper  ChatbotTheme android.content.ContextWrapper  ConcurrentHashMap android.content.ContextWrapper  Context android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  CustomerServiceListHandler android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  	Exception android.content.ContextWrapper  FloatingWindowService android.content.ContextWrapper  GLOBAL_ACTION_BACK android.content.ContextWrapper  Gravity android.content.ContextWrapper  Handler android.content.ContextWrapper  IllegalArgumentException android.content.ContextWrapper  ImageButton android.content.ContextWrapper  Intent android.content.ContextWrapper  LayoutInflater android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  MainContent android.content.ContextWrapper  Math android.content.ContextWrapper  MessageGroup android.content.ContextWrapper  Modifier android.content.ContextWrapper  MotionEvent android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  PixelFormat android.content.ContextWrapper  R android.content.ContextWrapper  Rect android.content.ContextWrapper  Regex android.content.ContextWrapper  Runnable android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  Scaffold android.content.ContextWrapper  Settings android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  TAG android.content.ContextWrapper  Toast android.content.ContextWrapper  Uri android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  android android.content.ContextWrapper  any android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  cancel android.content.ContextWrapper  chatHistoryCallback android.content.ContextWrapper  contains android.content.ContextWrapper  delay android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  extractChatHistory android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  findNodeByClassName android.content.ContextWrapper  findNodeByText android.content.ContextWrapper  findScrollableNode android.content.ContextWrapper  getInstance android.content.ContextWrapper  getSystemService android.content.ContextWrapper  handleCustomerServicePage android.content.ContextWrapper  instance android.content.ContextWrapper  isBlank android.content.ContextWrapper  isExtractionRunning android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  listOf android.content.ContextWrapper  matches android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  packageName android.content.ContextWrapper  padding android.content.ContextWrapper  	resources android.content.ContextWrapper  rootInActiveWindow android.content.ContextWrapper  set android.content.ContextWrapper  
setContent android.content.ContextWrapper  sortedBy android.content.ContextWrapper  startForegroundService android.content.ContextWrapper  startService android.content.ContextWrapper  
startsWith android.content.ContextWrapper  until android.content.ContextWrapper  withContext android.content.ContextWrapper  action android.content.Intent  displayMetrics android.content.res.Resources  AccessibilityNodeInfo android.graphics  AccessibilityService android.graphics  Boolean android.graphics  Canvas android.graphics  Color android.graphics  Context android.graphics  	Exception android.graphics  GestureDescription android.graphics  Gravity android.graphics  Handler android.graphics  Int android.graphics  List android.graphics  Log android.graphics  Looper android.graphics  MutableList android.graphics  Paint android.graphics  Path android.graphics  PixelFormat android.graphics  Rect android.graphics  Regex android.graphics  String android.graphics  Suppress android.graphics  TAG android.graphics  TARGET_PACKAGE android.graphics  Thread android.graphics  View android.graphics  
WindowManager android.graphics  android android.graphics  apply android.graphics  contains android.graphics  forEachIndexed android.graphics  getValue android.graphics  
isNotEmpty android.graphics  lazy android.graphics  let android.graphics  matches android.graphics  
mutableListOf android.graphics  provideDelegate android.graphics  repeat android.graphics  substringAfterLast android.graphics  take android.graphics  toIntOrNull android.graphics  trim android.graphics  until android.graphics  	withIndex android.graphics  GestureResultCallback %android.graphics.AccessibilityService  
drawCircle android.graphics.Canvas  RED android.graphics.Color  WHITE android.graphics.Color  Color android.graphics.Paint  Paint android.graphics.Paint  Style android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  isAntiAlias android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  apply android.graphics.Path  moveTo android.graphics.Path  TRANSLUCENT android.graphics.PixelFormat  bottom android.graphics.Rect  centerX android.graphics.Rect  centerY android.graphics.Rect  height android.graphics.Rect  isEmpty android.graphics.Rect  left android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  putCharSequence android.os.Bundle  post android.os.Handler  postDelayed android.os.Handler  removeCallbacks android.os.Handler  removeCallbacksAndMessages android.os.Handler  
getMainLooper android.os.Looper  Settings android.provider  ACTION_ACCESSIBILITY_SETTINGS android.provider.Settings   ACTION_MANAGE_OVERLAY_PERMISSION android.provider.Settings  canDrawOverlays android.provider.Settings  Log android.util  density android.util.DisplayMetrics  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  Gravity android.view  LayoutInflater android.view  MotionEvent android.view  View android.view  	ViewGroup android.view  ViewPropertyAnimator android.view  
WindowManager android.view  AccessibilityManager  android.view.ContextThemeWrapper  AccessibilityServiceInfo  android.view.ContextThemeWrapper  ActivityResultContracts  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  ChatbotTheme  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  FloatingWindowService  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  MainContent  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  START android.view.Gravity  TOP android.view.Gravity  from android.view.LayoutInflater  inflate android.view.LayoutInflater  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  rawX android.view.MotionEvent  rawY android.view.MotionEvent  Color android.view.View  OnClickListener android.view.View  OnTouchListener android.view.View  Paint android.view.View  alpha android.view.View  animate android.view.View  apply android.view.View  context android.view.View  findViewById android.view.View  isAttachedToWindow android.view.View  let android.view.View  onDraw android.view.View  performClick android.view.View  setOnClickListener android.view.View  setOnTouchListener android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> !android.view.View.OnTouchListener  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  height #android.view.ViewGroup.LayoutParams  width #android.view.ViewGroup.LayoutParams  addView android.view.ViewManager  
removeView android.view.ViewManager  updateViewLayout android.view.ViewManager  alpha !android.view.ViewPropertyAnimator  setDuration !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  
withEndAction !android.view.ViewPropertyAnimator  LayoutParams android.view.WindowManager  addView android.view.WindowManager  
removeView android.view.WindowManager  updateViewLayout android.view.WindowManager  FLAG_NOT_FOCUSABLE 'android.view.WindowManager.LayoutParams  FLAG_NOT_TOUCHABLE 'android.view.WindowManager.LayoutParams  Gravity 'android.view.WindowManager.LayoutParams  PixelFormat 'android.view.WindowManager.LayoutParams  TYPE_APPLICATION_OVERLAY 'android.view.WindowManager.LayoutParams  
TYPE_PHONE 'android.view.WindowManager.LayoutParams  WRAP_CONTENT 'android.view.WindowManager.LayoutParams  
WindowManager 'android.view.WindowManager.LayoutParams  android 'android.view.WindowManager.LayoutParams  apply 'android.view.WindowManager.LayoutParams  flags 'android.view.WindowManager.LayoutParams  format 'android.view.WindowManager.LayoutParams  gravity 'android.view.WindowManager.LayoutParams  height 'android.view.WindowManager.LayoutParams  	resources 'android.view.WindowManager.LayoutParams  type 'android.view.WindowManager.LayoutParams  width 'android.view.WindowManager.LayoutParams  x 'android.view.WindowManager.LayoutParams  y 'android.view.WindowManager.LayoutParams  AccessibilityEvent android.view.accessibility  AccessibilityManager android.view.accessibility  AccessibilityNodeInfo android.view.accessibility  TYPE_VIEW_CLICKED -android.view.accessibility.AccessibilityEvent  TYPE_VIEW_FOCUSED -android.view.accessibility.AccessibilityEvent  TYPE_VIEW_SCROLLED -android.view.accessibility.AccessibilityEvent  TYPE_WINDOW_CONTENT_CHANGED -android.view.accessibility.AccessibilityEvent  TYPE_WINDOW_STATE_CHANGED -android.view.accessibility.AccessibilityEvent  	eventType -android.view.accessibility.AccessibilityEvent  packageName -android.view.accessibility.AccessibilityEvent  source -android.view.accessibility.AccessibilityEvent  "getEnabledAccessibilityServiceList /android.view.accessibility.AccessibilityManager  %ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE 0android.view.accessibility.AccessibilityNodeInfo  ACTION_CLICK 0android.view.accessibility.AccessibilityNodeInfo  ACTION_SCROLL_BACKWARD 0android.view.accessibility.AccessibilityNodeInfo  ACTION_SET_TEXT 0android.view.accessibility.AccessibilityNodeInfo  
childCount 0android.view.accessibility.AccessibilityNodeInfo  	className 0android.view.accessibility.AccessibilityNodeInfo  getBoundsInScreen 0android.view.accessibility.AccessibilityNodeInfo  getChild 0android.view.accessibility.AccessibilityNodeInfo  isClickable 0android.view.accessibility.AccessibilityNodeInfo  isScrollable 0android.view.accessibility.AccessibilityNodeInfo  let 0android.view.accessibility.AccessibilityNodeInfo  packageName 0android.view.accessibility.AccessibilityNodeInfo  
performAction 0android.view.accessibility.AccessibilityNodeInfo  recycle 0android.view.accessibility.AccessibilityNodeInfo  text 0android.view.accessibility.AccessibilityNodeInfo  source .android.view.accessibility.AccessibilityRecord  ImageButton android.widget  Toast android.widget  setImageResource android.widget.ImageView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AccessibilityManager #androidx.activity.ComponentActivity  AccessibilityServiceInfo #androidx.activity.ComponentActivity  ActivityResultContracts #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  ChatbotTheme #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  FloatingWindowService #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  MainContent #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  Settings #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  contains #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  isSystemInDarkTheme androidx.compose.foundation  AccessibilityManager "androidx.compose.foundation.layout  AccessibilityServiceInfo "androidx.compose.foundation.layout  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  ChatbotTheme "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  FloatingWindowService "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  MainContent "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Text +androidx.compose.foundation.layout.RowScope  AccessibilityManager androidx.compose.material3  AccessibilityServiceInfo androidx.compose.material3  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Build androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  ChatbotTheme androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  Context androidx.compose.material3  FloatingWindowService androidx.compose.material3  Intent androidx.compose.material3  MainContent androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Scaffold androidx.compose.material3  Settings androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  Toast androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  buttonColors androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  height androidx.compose.material3  java androidx.compose.material3  lightColorScheme androidx.compose.material3  padding androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  error &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  AccessibilityManager androidx.compose.runtime  AccessibilityServiceInfo androidx.compose.runtime  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Build androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  ChatbotTheme androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  Context androidx.compose.runtime  FloatingWindowService androidx.compose.runtime  Intent androidx.compose.runtime  MainContent androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Scaffold androidx.compose.runtime  Settings androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  Toast androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  buttonColors androidx.compose.runtime  contains androidx.compose.runtime  fillMaxSize androidx.compose.runtime  height androidx.compose.runtime  java androidx.compose.runtime  padding androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  NotificationCompat androidx.core.app  AccessibilityManager #androidx.core.app.ComponentActivity  AccessibilityServiceInfo #androidx.core.app.ComponentActivity  ActivityResultContracts #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  ChatbotTheme #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  FloatingWindowService #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  MainContent #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  Settings #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  contains #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  AccessibilityNodeInfoCompat  androidx.core.view.accessibility  AccessibilityManager com.coffee.chatbot  AccessibilityServiceInfo com.coffee.chatbot  ActivityResultContracts com.coffee.chatbot  	Alignment com.coffee.chatbot  Arrangement com.coffee.chatbot  Boolean com.coffee.chatbot  Build com.coffee.chatbot  Bundle com.coffee.chatbot  Button com.coffee.chatbot  ButtonDefaults com.coffee.chatbot  ChatbotTheme com.coffee.chatbot  Column com.coffee.chatbot  ComponentActivity com.coffee.chatbot  
Composable com.coffee.chatbot  Context com.coffee.chatbot  FloatingWindowService com.coffee.chatbot  Intent com.coffee.chatbot  MainActivity com.coffee.chatbot  MainContent com.coffee.chatbot  
MaterialTheme com.coffee.chatbot  Modifier com.coffee.chatbot  R com.coffee.chatbot  Scaffold com.coffee.chatbot  Settings com.coffee.chatbot  Text com.coffee.chatbot  	TextAlign com.coffee.chatbot  Toast com.coffee.chatbot  Unit com.coffee.chatbot  Uri com.coffee.chatbot  buttonColors com.coffee.chatbot  contains com.coffee.chatbot  fillMaxSize com.coffee.chatbot  height com.coffee.chatbot  java com.coffee.chatbot  padding com.coffee.chatbot  width com.coffee.chatbot  AccessibilityServiceInfo com.coffee.chatbot.MainActivity  ActivityResultContracts com.coffee.chatbot.MainActivity  Build com.coffee.chatbot.MainActivity  ChatbotTheme com.coffee.chatbot.MainActivity  Context com.coffee.chatbot.MainActivity  FloatingWindowService com.coffee.chatbot.MainActivity  Intent com.coffee.chatbot.MainActivity  MainContent com.coffee.chatbot.MainActivity  Modifier com.coffee.chatbot.MainActivity  Scaffold com.coffee.chatbot.MainActivity  Settings com.coffee.chatbot.MainActivity  Toast com.coffee.chatbot.MainActivity  Uri com.coffee.chatbot.MainActivity  checkAccessibilityPermission com.coffee.chatbot.MainActivity  checkAndRequestPermissions com.coffee.chatbot.MainActivity  contains com.coffee.chatbot.MainActivity  enableEdgeToEdge com.coffee.chatbot.MainActivity  fillMaxSize com.coffee.chatbot.MainActivity  getSystemService com.coffee.chatbot.MainActivity  isAccessibilityServiceEnabled com.coffee.chatbot.MainActivity  isFloatingServiceRunning com.coffee.chatbot.MainActivity  isServiceRunning com.coffee.chatbot.MainActivity  java com.coffee.chatbot.MainActivity  overlayPermissionLauncher com.coffee.chatbot.MainActivity  packageName com.coffee.chatbot.MainActivity  padding com.coffee.chatbot.MainActivity  registerForActivityResult com.coffee.chatbot.MainActivity  requestAccessibilityPermission com.coffee.chatbot.MainActivity  requestOverlayPermission com.coffee.chatbot.MainActivity  
setContent com.coffee.chatbot.MainActivity  
startActivity com.coffee.chatbot.MainActivity  startFloatingWindowService com.coffee.chatbot.MainActivity  startForegroundService com.coffee.chatbot.MainActivity  startService com.coffee.chatbot.MainActivity  stopFloatingWindowService com.coffee.chatbot.MainActivity  updateUI com.coffee.chatbot.MainActivity  ic_launcher_foreground com.coffee.chatbot.R.drawable  floating_button com.coffee.chatbot.R.id  floating_button_layout com.coffee.chatbot.R.layout  ChatMessage com.coffee.chatbot.model  Long com.coffee.chatbot.model  Rect com.coffee.chatbot.model  String com.coffee.chatbot.model  System com.coffee.chatbot.model  bounds $com.coffee.chatbot.model.ChatMessage  AccessibilityEvent com.coffee.chatbot.service  AccessibilityNodeInfo com.coffee.chatbot.service  AccessibilityService com.coffee.chatbot.service  Boolean com.coffee.chatbot.service  Build com.coffee.chatbot.service  Bundle com.coffee.chatbot.service  
CHANNEL_ID com.coffee.chatbot.service  Canvas com.coffee.chatbot.service  ChatMessage com.coffee.chatbot.service  ChatbotAccessibilityService com.coffee.chatbot.service  Color com.coffee.chatbot.service  ConcurrentHashMap com.coffee.chatbot.service  Context com.coffee.chatbot.service  CoroutineScope com.coffee.chatbot.service  CustomerServiceListHandler com.coffee.chatbot.service  Dispatchers com.coffee.chatbot.service  	Exception com.coffee.chatbot.service  FloatingWindowService com.coffee.chatbot.service  GLOBAL_ACTION_BACK com.coffee.chatbot.service  GestureDescription com.coffee.chatbot.service  Gravity com.coffee.chatbot.service  Handler com.coffee.chatbot.service  IBinder com.coffee.chatbot.service  IllegalArgumentException com.coffee.chatbot.service  ImageButton com.coffee.chatbot.service  Int com.coffee.chatbot.service  Intent com.coffee.chatbot.service  LayoutInflater com.coffee.chatbot.service  List com.coffee.chatbot.service  Log com.coffee.chatbot.service  Looper com.coffee.chatbot.service  Math com.coffee.chatbot.service  MessageGroup com.coffee.chatbot.service  MotionEvent com.coffee.chatbot.service  MutableList com.coffee.chatbot.service  NOTIFICATION_ID com.coffee.chatbot.service  Notification com.coffee.chatbot.service  NotificationChannel com.coffee.chatbot.service  NotificationCompat com.coffee.chatbot.service  NotificationManager com.coffee.chatbot.service  Paint com.coffee.chatbot.service  Path com.coffee.chatbot.service  PixelFormat com.coffee.chatbot.service  R com.coffee.chatbot.service  Rect com.coffee.chatbot.service  Regex com.coffee.chatbot.service  Runnable com.coffee.chatbot.service  START_NOT_STICKY com.coffee.chatbot.service  START_STICKY com.coffee.chatbot.service  Service com.coffee.chatbot.service  String com.coffee.chatbot.service  
SupervisorJob com.coffee.chatbot.service  Suppress com.coffee.chatbot.service  TAG com.coffee.chatbot.service  TARGET_PACKAGE com.coffee.chatbot.service  Thread com.coffee.chatbot.service  Toast com.coffee.chatbot.service  Unit com.coffee.chatbot.service  View com.coffee.chatbot.service  Volatile com.coffee.chatbot.service  
WindowManager com.coffee.chatbot.service  android com.coffee.chatbot.service  any com.coffee.chatbot.service  apply com.coffee.chatbot.service  cancel com.coffee.chatbot.service  chatHistoryCallback com.coffee.chatbot.service  contains com.coffee.chatbot.service  delay com.coffee.chatbot.service  extractChatHistory com.coffee.chatbot.service  findNodeByClassName com.coffee.chatbot.service  findNodeByText com.coffee.chatbot.service  findScrollableNode com.coffee.chatbot.service  forEachIndexed com.coffee.chatbot.service  getInstance com.coffee.chatbot.service  getValue com.coffee.chatbot.service  handleCustomerServicePage com.coffee.chatbot.service  instance com.coffee.chatbot.service  isBlank com.coffee.chatbot.service  isExtractionRunning com.coffee.chatbot.service  
isInitialized com.coffee.chatbot.service  
isNotEmpty com.coffee.chatbot.service  java com.coffee.chatbot.service  launch com.coffee.chatbot.service  lazy com.coffee.chatbot.service  let com.coffee.chatbot.service  listOf com.coffee.chatbot.service  matches com.coffee.chatbot.service  
mutableListOf com.coffee.chatbot.service  provideDelegate com.coffee.chatbot.service  repeat com.coffee.chatbot.service  	resources com.coffee.chatbot.service  rootInActiveWindow com.coffee.chatbot.service  set com.coffee.chatbot.service  sortedBy com.coffee.chatbot.service  
startsWith com.coffee.chatbot.service  substringAfterLast com.coffee.chatbot.service  take com.coffee.chatbot.service  toIntOrNull com.coffee.chatbot.service  trim com.coffee.chatbot.service  until com.coffee.chatbot.service  withContext com.coffee.chatbot.service  	withIndex com.coffee.chatbot.service  GestureResultCallback /com.coffee.chatbot.service.AccessibilityService  AccessibilityEvent 6com.coffee.chatbot.service.ChatbotAccessibilityService  AccessibilityNodeInfo 6com.coffee.chatbot.service.ChatbotAccessibilityService  Boolean 6com.coffee.chatbot.service.ChatbotAccessibilityService  Bundle 6com.coffee.chatbot.service.ChatbotAccessibilityService  ChatMessage 6com.coffee.chatbot.service.ChatbotAccessibilityService  ChatbotAccessibilityService 6com.coffee.chatbot.service.ChatbotAccessibilityService  	Companion 6com.coffee.chatbot.service.ChatbotAccessibilityService  ConcurrentHashMap 6com.coffee.chatbot.service.ChatbotAccessibilityService  CoroutineScope 6com.coffee.chatbot.service.ChatbotAccessibilityService  CustomerServiceListHandler 6com.coffee.chatbot.service.ChatbotAccessibilityService  Dispatchers 6com.coffee.chatbot.service.ChatbotAccessibilityService  	Exception 6com.coffee.chatbot.service.ChatbotAccessibilityService  GLOBAL_ACTION_BACK 6com.coffee.chatbot.service.ChatbotAccessibilityService  Handler 6com.coffee.chatbot.service.ChatbotAccessibilityService  Int 6com.coffee.chatbot.service.ChatbotAccessibilityService  List 6com.coffee.chatbot.service.ChatbotAccessibilityService  Log 6com.coffee.chatbot.service.ChatbotAccessibilityService  Looper 6com.coffee.chatbot.service.ChatbotAccessibilityService  MessageGroup 6com.coffee.chatbot.service.ChatbotAccessibilityService  MutableList 6com.coffee.chatbot.service.ChatbotAccessibilityService  Rect 6com.coffee.chatbot.service.ChatbotAccessibilityService  Regex 6com.coffee.chatbot.service.ChatbotAccessibilityService  Runnable 6com.coffee.chatbot.service.ChatbotAccessibilityService  String 6com.coffee.chatbot.service.ChatbotAccessibilityService  
SupervisorJob 6com.coffee.chatbot.service.ChatbotAccessibilityService  TAG 6com.coffee.chatbot.service.ChatbotAccessibilityService  Unit 6com.coffee.chatbot.service.ChatbotAccessibilityService  Volatile 6com.coffee.chatbot.service.ChatbotAccessibilityService  any 6com.coffee.chatbot.service.ChatbotAccessibilityService  applicationContext 6com.coffee.chatbot.service.ChatbotAccessibilityService  apply 6com.coffee.chatbot.service.ChatbotAccessibilityService  cancel 6com.coffee.chatbot.service.ChatbotAccessibilityService  chatHistoryCallback 6com.coffee.chatbot.service.ChatbotAccessibilityService  checkForNewMessages 6com.coffee.chatbot.service.ChatbotAccessibilityService  contains 6com.coffee.chatbot.service.ChatbotAccessibilityService  coroutineScope 6com.coffee.chatbot.service.ChatbotAccessibilityService  customerServiceHandler 6com.coffee.chatbot.service.ChatbotAccessibilityService  delay 6com.coffee.chatbot.service.ChatbotAccessibilityService  eventTypeToString 6com.coffee.chatbot.service.ChatbotAccessibilityService  extractChatHistory 6com.coffee.chatbot.service.ChatbotAccessibilityService  extractedMessages 6com.coffee.chatbot.service.ChatbotAccessibilityService  findAllNodesOfType 6com.coffee.chatbot.service.ChatbotAccessibilityService  findAllNodesOfTypeRecursive 6com.coffee.chatbot.service.ChatbotAccessibilityService  findNodeByClassName 6com.coffee.chatbot.service.ChatbotAccessibilityService  findNodeByText 6com.coffee.chatbot.service.ChatbotAccessibilityService  findScrollableNode 6com.coffee.chatbot.service.ChatbotAccessibilityService  getInstance 6com.coffee.chatbot.service.ChatbotAccessibilityService  handleCustomerServiceList 6com.coffee.chatbot.service.ChatbotAccessibilityService  handleCustomerServicePage 6com.coffee.chatbot.service.ChatbotAccessibilityService  instance 6com.coffee.chatbot.service.ChatbotAccessibilityService  isBlank 6com.coffee.chatbot.service.ChatbotAccessibilityService  isExtractionRunning 6com.coffee.chatbot.service.ChatbotAccessibilityService  launch 6com.coffee.chatbot.service.ChatbotAccessibilityService  listOf 6com.coffee.chatbot.service.ChatbotAccessibilityService  matches 6com.coffee.chatbot.service.ChatbotAccessibilityService  monitoringHandler 6com.coffee.chatbot.service.ChatbotAccessibilityService  monitoringInterval 6com.coffee.chatbot.service.ChatbotAccessibilityService  monitoringRunnable 6com.coffee.chatbot.service.ChatbotAccessibilityService  
mutableListOf 6com.coffee.chatbot.service.ChatbotAccessibilityService  performGlobalAction 6com.coffee.chatbot.service.ChatbotAccessibilityService  	resources 6com.coffee.chatbot.service.ChatbotAccessibilityService  rootInActiveWindow 6com.coffee.chatbot.service.ChatbotAccessibilityService  set 6com.coffee.chatbot.service.ChatbotAccessibilityService  sortedBy 6com.coffee.chatbot.service.ChatbotAccessibilityService  startChatExtraction 6com.coffee.chatbot.service.ChatbotAccessibilityService  startListeningForNewMessages 6com.coffee.chatbot.service.ChatbotAccessibilityService  
startsWith 6com.coffee.chatbot.service.ChatbotAccessibilityService  stopChatExtraction 6com.coffee.chatbot.service.ChatbotAccessibilityService  stopListeningForNewMessages 6com.coffee.chatbot.service.ChatbotAccessibilityService  until 6com.coffee.chatbot.service.ChatbotAccessibilityService  withContext 6com.coffee.chatbot.service.ChatbotAccessibilityService  AccessibilityEvent @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  AccessibilityNodeInfo @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Bundle @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  ChatMessage @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  ConcurrentHashMap @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  CoroutineScope @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  CustomerServiceListHandler @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Dispatchers @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  GLOBAL_ACTION_BACK @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Handler @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Log @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Looper @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  MessageGroup @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Rect @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Regex @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Runnable @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  
SupervisorJob @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  TAG @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  any @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  apply @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  cancel @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  chatHistoryCallback @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  contains @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  delay @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  extractChatHistory @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  findNodeByClassName @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  findNodeByText @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  findScrollableNode @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  getInstance @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  handleCustomerServicePage @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  instance @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  isBlank @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  isExtractionRunning @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  launch @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  listOf @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  matches @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  
mutableListOf @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  rootInActiveWindow @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  set @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  sortedBy @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  
startsWith @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  until @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  withContext @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  bounds Ccom.coffee.chatbot.service.ChatbotAccessibilityService.MessageGroup  text Ccom.coffee.chatbot.service.ChatbotAccessibilityService.MessageGroup  type Ccom.coffee.chatbot.service.ChatbotAccessibilityService.MessageGroup  AccessibilityNodeInfo 5com.coffee.chatbot.service.CustomerServiceListHandler  AccessibilityService 5com.coffee.chatbot.service.CustomerServiceListHandler  Boolean 5com.coffee.chatbot.service.CustomerServiceListHandler  Canvas 5com.coffee.chatbot.service.CustomerServiceListHandler  Color 5com.coffee.chatbot.service.CustomerServiceListHandler  Context 5com.coffee.chatbot.service.CustomerServiceListHandler  	Exception 5com.coffee.chatbot.service.CustomerServiceListHandler  GestureDescription 5com.coffee.chatbot.service.CustomerServiceListHandler  Gravity 5com.coffee.chatbot.service.CustomerServiceListHandler  Handler 5com.coffee.chatbot.service.CustomerServiceListHandler  Int 5com.coffee.chatbot.service.CustomerServiceListHandler  List 5com.coffee.chatbot.service.CustomerServiceListHandler  Log 5com.coffee.chatbot.service.CustomerServiceListHandler  Looper 5com.coffee.chatbot.service.CustomerServiceListHandler  MutableList 5com.coffee.chatbot.service.CustomerServiceListHandler  Paint 5com.coffee.chatbot.service.CustomerServiceListHandler  Path 5com.coffee.chatbot.service.CustomerServiceListHandler  PixelFormat 5com.coffee.chatbot.service.CustomerServiceListHandler  Rect 5com.coffee.chatbot.service.CustomerServiceListHandler  Regex 5com.coffee.chatbot.service.CustomerServiceListHandler  String 5com.coffee.chatbot.service.CustomerServiceListHandler  Suppress 5com.coffee.chatbot.service.CustomerServiceListHandler  TAG 5com.coffee.chatbot.service.CustomerServiceListHandler  TARGET_PACKAGE 5com.coffee.chatbot.service.CustomerServiceListHandler  Thread 5com.coffee.chatbot.service.CustomerServiceListHandler  View 5com.coffee.chatbot.service.CustomerServiceListHandler  
WindowManager 5com.coffee.chatbot.service.CustomerServiceListHandler  accessibilityService 5com.coffee.chatbot.service.CustomerServiceListHandler  android 5com.coffee.chatbot.service.CustomerServiceListHandler  apply 5com.coffee.chatbot.service.CustomerServiceListHandler  checkAndEnterNewMessage 5com.coffee.chatbot.service.CustomerServiceListHandler  clickConversation 5com.coffee.chatbot.service.CustomerServiceListHandler  collectConversations 5com.coffee.chatbot.service.CustomerServiceListHandler  contains 5com.coffee.chatbot.service.CustomerServiceListHandler  containsTimeInfo 5com.coffee.chatbot.service.CustomerServiceListHandler  context 5com.coffee.chatbot.service.CustomerServiceListHandler  debugClickPosition 5com.coffee.chatbot.service.CustomerServiceListHandler  dumpNodeRecursive 5com.coffee.chatbot.service.CustomerServiceListHandler  findAllTextViews 5com.coffee.chatbot.service.CustomerServiceListHandler  findAndClickUnreadConversation 5com.coffee.chatbot.service.CustomerServiceListHandler  findClickableNode 5com.coffee.chatbot.service.CustomerServiceListHandler  findContainersRecursive 5com.coffee.chatbot.service.CustomerServiceListHandler  findConversationContainers 5com.coffee.chatbot.service.CustomerServiceListHandler  findNodesByText 5com.coffee.chatbot.service.CustomerServiceListHandler  findNodesByTextRecursive 5com.coffee.chatbot.service.CustomerServiceListHandler  findTextRecursive 5com.coffee.chatbot.service.CustomerServiceListHandler  findUnreadCount 5com.coffee.chatbot.service.CustomerServiceListHandler  findUnreadCountRecursive 5com.coffee.chatbot.service.CustomerServiceListHandler  findUnreadIndicator 5com.coffee.chatbot.service.CustomerServiceListHandler  findUnreadMessageBadgeRecursive 5com.coffee.chatbot.service.CustomerServiceListHandler  findWaitingText 5com.coffee.chatbot.service.CustomerServiceListHandler  forEachIndexed 5com.coffee.chatbot.service.CustomerServiceListHandler  	getBounds 5com.coffee.chatbot.service.CustomerServiceListHandler  getConversationsFromContainer 5com.coffee.chatbot.service.CustomerServiceListHandler  getValue 5com.coffee.chatbot.service.CustomerServiceListHandler  handler 5com.coffee.chatbot.service.CustomerServiceListHandler  hasUnreadMessage 5com.coffee.chatbot.service.CustomerServiceListHandler  hideClickMarker 5com.coffee.chatbot.service.CustomerServiceListHandler  isConversationItem 5com.coffee.chatbot.service.CustomerServiceListHandler  isInCustomerServicePage 5com.coffee.chatbot.service.CustomerServiceListHandler  
isNotEmpty 5com.coffee.chatbot.service.CustomerServiceListHandler  isTargetApp 5com.coffee.chatbot.service.CustomerServiceListHandler  lazy 5com.coffee.chatbot.service.CustomerServiceListHandler  let 5com.coffee.chatbot.service.CustomerServiceListHandler  
markerView 5com.coffee.chatbot.service.CustomerServiceListHandler  matches 5com.coffee.chatbot.service.CustomerServiceListHandler  
mutableListOf 5com.coffee.chatbot.service.CustomerServiceListHandler  performClick 5com.coffee.chatbot.service.CustomerServiceListHandler  performGestureClick 5com.coffee.chatbot.service.CustomerServiceListHandler  provideDelegate 5com.coffee.chatbot.service.CustomerServiceListHandler  repeat 5com.coffee.chatbot.service.CustomerServiceListHandler  setAccessibilityService 5com.coffee.chatbot.service.CustomerServiceListHandler  showClickMarker 5com.coffee.chatbot.service.CustomerServiceListHandler  substringAfterLast 5com.coffee.chatbot.service.CustomerServiceListHandler  take 5com.coffee.chatbot.service.CustomerServiceListHandler  toIntOrNull 5com.coffee.chatbot.service.CustomerServiceListHandler  trim 5com.coffee.chatbot.service.CustomerServiceListHandler  tryDirectNodeClick 5com.coffee.chatbot.service.CustomerServiceListHandler  tryDoubleClick 5com.coffee.chatbot.service.CustomerServiceListHandler  tryLongPress 5com.coffee.chatbot.service.CustomerServiceListHandler  tryMultipleGestureStrategies 5com.coffee.chatbot.service.CustomerServiceListHandler  tryStandardClick 5com.coffee.chatbot.service.CustomerServiceListHandler  until 5com.coffee.chatbot.service.CustomerServiceListHandler  
windowManager 5com.coffee.chatbot.service.CustomerServiceListHandler  	withIndex 5com.coffee.chatbot.service.CustomerServiceListHandler  GestureResultCallback Jcom.coffee.chatbot.service.CustomerServiceListHandler.AccessibilityService  AccessibilityNodeInfo ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Color ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Context ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  GestureDescription ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Gravity ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Handler ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Log ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Looper ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Paint ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Path ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  PixelFormat ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Rect ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Regex ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  TAG ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  TARGET_PACKAGE ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Thread ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  
WindowManager ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  android ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  apply ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  contains ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  forEachIndexed ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  getValue ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  
isNotEmpty ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  lazy ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  let ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  matches ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  
mutableListOf ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  provideDelegate ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  repeat ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  substringAfterLast ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  take ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  toIntOrNull ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  trim ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  until ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  	withIndex ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  Build 0com.coffee.chatbot.service.FloatingWindowService  
CHANNEL_ID 0com.coffee.chatbot.service.FloatingWindowService  ChatbotAccessibilityService 0com.coffee.chatbot.service.FloatingWindowService  	Companion 0com.coffee.chatbot.service.FloatingWindowService  Context 0com.coffee.chatbot.service.FloatingWindowService  CoroutineScope 0com.coffee.chatbot.service.FloatingWindowService  Dispatchers 0com.coffee.chatbot.service.FloatingWindowService  	Exception 0com.coffee.chatbot.service.FloatingWindowService  Gravity 0com.coffee.chatbot.service.FloatingWindowService  Handler 0com.coffee.chatbot.service.FloatingWindowService  IBinder 0com.coffee.chatbot.service.FloatingWindowService  IllegalArgumentException 0com.coffee.chatbot.service.FloatingWindowService  ImageButton 0com.coffee.chatbot.service.FloatingWindowService  Int 0com.coffee.chatbot.service.FloatingWindowService  Intent 0com.coffee.chatbot.service.FloatingWindowService  LayoutInflater 0com.coffee.chatbot.service.FloatingWindowService  Log 0com.coffee.chatbot.service.FloatingWindowService  Looper 0com.coffee.chatbot.service.FloatingWindowService  Math 0com.coffee.chatbot.service.FloatingWindowService  MotionEvent 0com.coffee.chatbot.service.FloatingWindowService  NOTIFICATION_ID 0com.coffee.chatbot.service.FloatingWindowService  Notification 0com.coffee.chatbot.service.FloatingWindowService  NotificationChannel 0com.coffee.chatbot.service.FloatingWindowService  NotificationCompat 0com.coffee.chatbot.service.FloatingWindowService  NotificationManager 0com.coffee.chatbot.service.FloatingWindowService  PixelFormat 0com.coffee.chatbot.service.FloatingWindowService  R 0com.coffee.chatbot.service.FloatingWindowService  START_NOT_STICKY 0com.coffee.chatbot.service.FloatingWindowService  START_STICKY 0com.coffee.chatbot.service.FloatingWindowService  
SupervisorJob 0com.coffee.chatbot.service.FloatingWindowService  TAG 0com.coffee.chatbot.service.FloatingWindowService  Toast 0com.coffee.chatbot.service.FloatingWindowService  View 0com.coffee.chatbot.service.FloatingWindowService  
WindowManager 0com.coffee.chatbot.service.FloatingWindowService  android 0com.coffee.chatbot.service.FloatingWindowService  apply 0com.coffee.chatbot.service.FloatingWindowService  checkBoundaries 0com.coffee.chatbot.service.FloatingWindowService  createFloatingWindow 0com.coffee.chatbot.service.FloatingWindowService  createNotification 0com.coffee.chatbot.service.FloatingWindowService  createNotificationChannel 0com.coffee.chatbot.service.FloatingWindowService  floatingView 0com.coffee.chatbot.service.FloatingWindowService  getInstance 0com.coffee.chatbot.service.FloatingWindowService  getSystemService 0com.coffee.chatbot.service.FloatingWindowService  handleChatAutomation 0com.coffee.chatbot.service.FloatingWindowService  isAutomationRunning 0com.coffee.chatbot.service.FloatingWindowService  
isInitialized 0com.coffee.chatbot.service.FloatingWindowService  isMonitoringMode 0com.coffee.chatbot.service.FloatingWindowService  java 0com.coffee.chatbot.service.FloatingWindowService  	resources 0com.coffee.chatbot.service.FloatingWindowService  setupDragMovement 0com.coffee.chatbot.service.FloatingWindowService  startForeground 0com.coffee.chatbot.service.FloatingWindowService  stopAutomation 0com.coffee.chatbot.service.FloatingWindowService  stopSelf 0com.coffee.chatbot.service.FloatingWindowService  toggleAutomation 0com.coffee.chatbot.service.FloatingWindowService  updateButtonForMonitoring 0com.coffee.chatbot.service.FloatingWindowService  
windowManager 0com.coffee.chatbot.service.FloatingWindowService  Build :com.coffee.chatbot.service.FloatingWindowService.Companion  
CHANNEL_ID :com.coffee.chatbot.service.FloatingWindowService.Companion  ChatbotAccessibilityService :com.coffee.chatbot.service.FloatingWindowService.Companion  Context :com.coffee.chatbot.service.FloatingWindowService.Companion  CoroutineScope :com.coffee.chatbot.service.FloatingWindowService.Companion  Dispatchers :com.coffee.chatbot.service.FloatingWindowService.Companion  Gravity :com.coffee.chatbot.service.FloatingWindowService.Companion  Handler :com.coffee.chatbot.service.FloatingWindowService.Companion  LayoutInflater :com.coffee.chatbot.service.FloatingWindowService.Companion  Log :com.coffee.chatbot.service.FloatingWindowService.Companion  Looper :com.coffee.chatbot.service.FloatingWindowService.Companion  Math :com.coffee.chatbot.service.FloatingWindowService.Companion  MotionEvent :com.coffee.chatbot.service.FloatingWindowService.Companion  NOTIFICATION_ID :com.coffee.chatbot.service.FloatingWindowService.Companion  NotificationChannel :com.coffee.chatbot.service.FloatingWindowService.Companion  NotificationCompat :com.coffee.chatbot.service.FloatingWindowService.Companion  NotificationManager :com.coffee.chatbot.service.FloatingWindowService.Companion  PixelFormat :com.coffee.chatbot.service.FloatingWindowService.Companion  R :com.coffee.chatbot.service.FloatingWindowService.Companion  START_NOT_STICKY :com.coffee.chatbot.service.FloatingWindowService.Companion  START_STICKY :com.coffee.chatbot.service.FloatingWindowService.Companion  
SupervisorJob :com.coffee.chatbot.service.FloatingWindowService.Companion  TAG :com.coffee.chatbot.service.FloatingWindowService.Companion  Toast :com.coffee.chatbot.service.FloatingWindowService.Companion  
WindowManager :com.coffee.chatbot.service.FloatingWindowService.Companion  android :com.coffee.chatbot.service.FloatingWindowService.Companion  apply :com.coffee.chatbot.service.FloatingWindowService.Companion  getInstance :com.coffee.chatbot.service.FloatingWindowService.Companion  
isInitialized :com.coffee.chatbot.service.FloatingWindowService.Companion  java :com.coffee.chatbot.service.FloatingWindowService.Companion  	resources :com.coffee.chatbot.service.FloatingWindowService.Companion  LayoutParams >com.coffee.chatbot.service.FloatingWindowService.WindowManager  LayoutParams (com.coffee.chatbot.service.WindowManager  Boolean com.coffee.chatbot.ui.theme  Build com.coffee.chatbot.ui.theme  ChatbotTheme com.coffee.chatbot.ui.theme  
Composable com.coffee.chatbot.ui.theme  DarkColorScheme com.coffee.chatbot.ui.theme  
FontFamily com.coffee.chatbot.ui.theme  
FontWeight com.coffee.chatbot.ui.theme  LightColorScheme com.coffee.chatbot.ui.theme  Pink40 com.coffee.chatbot.ui.theme  Pink80 com.coffee.chatbot.ui.theme  Purple40 com.coffee.chatbot.ui.theme  Purple80 com.coffee.chatbot.ui.theme  PurpleGrey40 com.coffee.chatbot.ui.theme  PurpleGrey80 com.coffee.chatbot.ui.theme  
Typography com.coffee.chatbot.ui.theme  Unit com.coffee.chatbot.ui.theme  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  Runnable 	java.lang  message java.lang.Exception  abs java.lang.Math  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  sleep java.lang.Thread  ConcurrentHashMap java.util.concurrent  clear &java.util.concurrent.ConcurrentHashMap  containsKey &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  values &java.util.concurrent.ConcurrentHashMap  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Lazy kotlin  Nothing kotlin  Suppress kotlin  apply kotlin  getValue kotlin  
isInitialized kotlin  lazy kotlin  let kotlin  repeat kotlin  toString 
kotlin.Any  not kotlin.Boolean  toString kotlin.CharSequence  sp 
kotlin.Double  toInt 
kotlin.Double  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  contains 
kotlin.String  isBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  matches 
kotlin.String  plus 
kotlin.String  repeat 
kotlin.String  
startsWith 
kotlin.String  substringAfterLast 
kotlin.String  take 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  message kotlin.Throwable  IndexedValue kotlin.collections  IntIterator kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  List kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  any kotlin.collections  contains kotlin.collections  forEachIndexed kotlin.collections  getValue kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  set kotlin.collections  sortedBy kotlin.collections  take kotlin.collections  	withIndex kotlin.collections  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  iterator kotlin.collections.Iterable  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  size kotlin.collections.List  	withIndex kotlin.collections.List  sortedBy $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  forEachIndexed kotlin.collections.MutableList  iterator kotlin.collections.MutableList  size kotlin.collections.MutableList  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  
startsWith 	kotlin.io  Volatile 
kotlin.jvm  java 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  forEachIndexed kotlin.sequences  sortedBy kotlin.sequences  take kotlin.sequences  	withIndex kotlin.sequences  Regex kotlin.text  any kotlin.text  contains kotlin.text  forEachIndexed kotlin.text  isBlank kotlin.text  
isNotEmpty kotlin.text  matches kotlin.text  repeat kotlin.text  set kotlin.text  
startsWith kotlin.text  substringAfterLast kotlin.text  take kotlin.text  toIntOrNull kotlin.text  trim kotlin.text  	withIndex kotlin.text  containsMatchIn kotlin.text.Regex  AccessibilityEvent kotlinx.coroutines  AccessibilityNodeInfo kotlinx.coroutines  AccessibilityService kotlinx.coroutines  Boolean kotlinx.coroutines  Bundle kotlinx.coroutines  ChatMessage kotlinx.coroutines  ChatbotAccessibilityService kotlinx.coroutines  CompletableJob kotlinx.coroutines  ConcurrentHashMap kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  CustomerServiceListHandler kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  	Exception kotlinx.coroutines  GLOBAL_ACTION_BACK kotlinx.coroutines  Handler kotlinx.coroutines  Int kotlinx.coroutines  Job kotlinx.coroutines  List kotlinx.coroutines  Log kotlinx.coroutines  Looper kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  MessageGroup kotlinx.coroutines  MutableList kotlinx.coroutines  Rect kotlinx.coroutines  Regex kotlinx.coroutines  Runnable kotlinx.coroutines  String kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  TAG kotlinx.coroutines  Unit kotlinx.coroutines  Volatile kotlinx.coroutines  any kotlinx.coroutines  apply kotlinx.coroutines  cancel kotlinx.coroutines  chatHistoryCallback kotlinx.coroutines  contains kotlinx.coroutines  delay kotlinx.coroutines  extractChatHistory kotlinx.coroutines  findNodeByClassName kotlinx.coroutines  findNodeByText kotlinx.coroutines  findScrollableNode kotlinx.coroutines  handleCustomerServicePage kotlinx.coroutines  instance kotlinx.coroutines  isBlank kotlinx.coroutines  isExtractionRunning kotlinx.coroutines  launch kotlinx.coroutines  listOf kotlinx.coroutines  matches kotlinx.coroutines  
mutableListOf kotlinx.coroutines  rootInActiveWindow kotlinx.coroutines  set kotlinx.coroutines  sortedBy kotlinx.coroutines  
startsWith kotlinx.coroutines  until kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  AccessibilityNodeInfo !kotlinx.coroutines.CoroutineScope  Bundle !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  extractChatHistory !kotlinx.coroutines.CoroutineScope  findNodeByClassName !kotlinx.coroutines.CoroutineScope  findNodeByText !kotlinx.coroutines.CoroutineScope  findScrollableNode !kotlinx.coroutines.CoroutineScope  handleCustomerServicePage !kotlinx.coroutines.CoroutineScope  isExtractionRunning !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  rootInActiveWindow !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  Default kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  plus *kotlinx.coroutines.MainCoroutineDispatcher  
StringBuilder android.graphics  contentDescription 0android.view.accessibility.AccessibilityNodeInfo  viewIdResourceName 0android.view.accessibility.AccessibilityNodeInfo  
StringBuilder com.coffee.chatbot.service  
StringBuilder 5com.coffee.chatbot.service.CustomerServiceListHandler  logNodeHierarchy 5com.coffee.chatbot.service.CustomerServiceListHandler  
StringBuilder ?com.coffee.chatbot.service.CustomerServiceListHandler.Companion  
StringBuilder 	java.lang  append java.lang.StringBuilder                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  