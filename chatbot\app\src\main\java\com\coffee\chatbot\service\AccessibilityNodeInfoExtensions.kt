package com.coffee.chatbot.service

import android.view.accessibility.AccessibilityNodeInfo
import java.util.regex.Pattern
import java.util.ArrayDeque

/**
 * 为 AccessibilityNodeInfo 提供 XPath 风格的查询功能。
 * 注意：这是一个增强的实现，用于提供强大的节点查找能力，但并非完全符合XPath 1.0规范。
 * 
 * 支持的基本语法:
 * - /: 直接子节点选择器, e.g., /FrameLayout/TextView
 * - //: 后代节点选择器, e.g., //TextView[@text='确定']
 * - ..: 父节点选择器, e.g., //TextView[@text='确定']/..
 * - .: 当前节点选择器, e.g., ./TextView (等同于/TextView)
 * - [index]: 索引谓词, e.g., /ViewGroup[0]
 * 
 * 支持的属性谓词:
 * - [@attribute='value']: 精确匹配属性值, e.g., [@text='确定']
 * - [@attribute!='value']: 不等于属性值, e.g., [@text!='取消']
 * - [@attribute~='value']: 包含属性值(子字符串), e.g., [@text~='确定']
 * - [@attribute^='value']: 属性值以指定字符串开头, e.g., [@text^='确']
 * - [@attribute$='value']: 属性值以指定字符串结尾, e.g., [@text$='定']
 * - [@attribute?='regex']: 属性值匹配正则表达式, e.g., [@text?='\\d+']
 * 
 * 支持的属性:
 * - text: 节点文本内容
 * - id: 资源ID (resource-id)
 * - desc: 内容描述 (content-description)
 * - pkg: 包名 (package name)
 * - cls: 类名 (class name)
 * - clickable: 是否可点击
 * - scrollable: 是否可滚动
 * - checked: 是否已选中
 * - selected: 是否已选择
 * - enabled: 是否已启用
 * - focused: 是否已获取焦点
 * - visible: 是否可见
 * 
 * 高级功能:
 * - *: 通配符，匹配任何类名
 * - [position()=n]: 位置谓词，选择特定位置的节点, e.g., //TextView[position()=1]
 * - [last()]: 选择最后一个节点, e.g., //TextView[last()]
 * - [last()-n]: 选择倒数第n+1个节点, e.g., //TextView[last()-1]
 * - [position()>n]: 选择位置大于n的所有节点
 * - [position()<n]: 选择位置小于n的所有节点
 * - [condition1 and condition2]: 多条件与操作, e.g., [@text='确定' and @clickable='true']
 * - [condition1 or condition2]: 多条件或操作, e.g., [@text='确定' or @text='取消']
 * - [not(condition)]: 条件取反, e.g., [not(@clickable='false')]
 */

// 数据类用于表示解析后的路径步骤
private data class PathStep(val axis: Axis, val segment: String)
private enum class Axis { SELF, CHILD, DESCENDANT, PARENT }

/**
 * 使用XPath表达式查找匹配的节点
 * @param xpath XPath表达式字符串
 * @return 匹配的AccessibilityNodeInfo列表
 */
fun AccessibilityNodeInfo.findNodesByXPath(xpath: String): List<AccessibilityNodeInfo> {
    val steps = tokenize(xpath)
    var currentNodes = setOf(this)

    for (step in steps) {
        val nextNodes = mutableSetOf<AccessibilityNodeInfo>()
        
        when {
            step.segment == ".." -> {
                // 处理父节点选择器 '..'
                currentNodes.forEach { node ->
                    node.parent?.let { parent -> nextNodes.add(parent) }
                }
            }
            step.segment == "." -> {
                // 处理当前节点选择器 '.'
                nextNodes.addAll(currentNodes)
            }
            else -> {
                // 处理常规节点选择
                val (className, predicates) = parseSegment(step.segment)
                for (node in currentNodes) {
                    val candidates = when (step.axis) {
                        Axis.DESCENDANT -> findAllDescendants(node)
                        Axis.CHILD -> (0 until node.childCount).mapNotNull { node.getChild(it) }
                        Axis.SELF -> listOf(node)
                        Axis.PARENT -> listOfNotNull(node.parent)
                    }

                    val matched = candidates.filterIndexed { index, child ->
                        val classMatch = (className == "*" || 
                                         child.className?.toString()?.endsWith(className) == true ||
                                         (className.isEmpty() && step.axis != Axis.CHILD)) // 允许//空节点表示所有后代
                        classMatch && matchesPredicates(child, index, predicates, candidates.size)
                    }
                    nextNodes.addAll(matched)

                    // 回收未匹配的候选节点
                    candidates.filterNot { matched.contains(it) }.forEach { it.recycle() }
                }
            }
        }
        currentNodes = nextNodes
    }

    return currentNodes.toList()
}

/**
 * 查找所有后代节点（不包括自身）
 * @param node 起始节点
 * @return 所有后代节点列表
 */
private fun findAllDescendants(node: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
    val result = mutableListOf<AccessibilityNodeInfo>()
    val queue = ArrayDeque<AccessibilityNodeInfo>()
    queue.addLast(node)
    
    while(queue.isNotEmpty()){
        val currentNode = queue.removeFirst()
        // 不把自己加进去，只加子孙
        // result.add(currentNode) 
        for (i in 0 until currentNode.childCount) {
            currentNode.getChild(i)?.let { child ->
                result.add(child)
                queue.addLast(child)
            }
        }
    }
    return result
}

/**
 * 将XPath字符串解析为步骤列表
 * @param xpath XPath表达式字符串
 * @return 解析后的路径步骤列表
 */
private fun tokenize(xpath: String): List<PathStep> {
    val steps = mutableListOf<PathStep>()
    // 使用正则表达式匹配 //, /, ., .. 和路径片段
    val pattern = Pattern.compile("(//|/|\\.\\.|\\.|)?([^/\\.]+|)")
    val matcher = pattern.matcher(xpath)
    
    while (matcher.find()) {
        val separator = matcher.group(1) ?: ""
        val segment = matcher.group(2) ?: ""
        
        if (separator.isEmpty() && segment.isEmpty()) continue
        
        val axis = when (separator) {
            "//" -> Axis.DESCENDANT
            "/" -> Axis.CHILD
            "." -> Axis.SELF
            ".." -> Axis.PARENT
            else -> Axis.CHILD // 默认为子节点轴
        }
        
        // 处理特殊情况：当分隔符是".."时，segment应该是空的
        val actualSegment = if (separator == "..") ".." else segment
        steps.add(PathStep(axis, actualSegment))
    }
    return steps
}

/**
 * 解析路径片段，提取类名和谓词列表
 * @param segment 路径片段字符串
 * @return 类名和谓词列表的对
 */
private fun parseSegment(segment: String): Pair<String, List<String>> {
    // 处理特殊情况
    if (segment == "." || segment == "..") {
        return Pair(segment, emptyList())
    }
    
    val predicatePattern = Pattern.compile("\\[(.*?)\\]")
    val predicateMatcher = predicatePattern.matcher(segment)
    val predicates = mutableListOf<String>()
    while (predicateMatcher.find()) {
        predicates.add(predicateMatcher.group(1))
    }
    
    val className = segment.replace(predicatePattern.toRegex(), "")
    return Pair(className, predicates)
}

/**
 * 检查节点是否匹配所有谓词条件
 * @param node 要检查的节点
 * @param index 节点在兄弟节点中的索引
 * @param predicates 谓词条件列表
 * @param totalCount 兄弟节点总数
 * @return 是否匹配所有谓词
 */
private fun matchesPredicates(node: AccessibilityNodeInfo, index: Int, predicates: List<String>, totalCount: Int = 0): Boolean {
    if (predicates.isEmpty()) return true

    return predicates.all { predicate ->
        when {
            // 索引谓词, e.g., [0]
            predicate.matches(Regex("\\d+")) -> 
                index == predicate.toInt()
            
            // 位置函数谓词
            predicate.startsWith("position()") -> 
                evaluatePositionPredicate(predicate, index)
            
            // last()函数谓词
            predicate.startsWith("last()") -> 
                evaluateLastPredicate(predicate, index, totalCount)
            
            // 多条件组合谓词
            predicate.contains(" and ") -> {
                val conditions = predicate.split(" and ")
                conditions.all { matchesPredicates(node, index, listOf(it.trim()), totalCount) }
            }
            
            predicate.contains(" or ") -> {
                val conditions = predicate.split(" or ")
                conditions.any { matchesPredicates(node, index, listOf(it.trim()), totalCount) }
            }
            
            // not()函数谓词
            predicate.startsWith("not(") && predicate.endsWith(")") -> {
                val innerPredicate = predicate.substring(4, predicate.length - 1)
                !matchesPredicates(node, index, listOf(innerPredicate), totalCount)
            }
            
            // 属性谓词, e.g., [@text='确定']
            else -> {
                val attrPattern = Pattern.compile("@(\\w+)(==|!=|~=|\\^=|\\$=|\\?=)['\"](.*?)['\"]")
                val attrMatcher = attrPattern.matcher(predicate)
                if (attrMatcher.matches()) {
                    val attr = attrMatcher.group(1)
                    val operator = attrMatcher.group(2)
                    val value = attrMatcher.group(3)
                    matchesAttribute(node, attr, value, operator)
                } else {
                    // 尝试匹配简单属性谓词 [@attr='value']
                    val simpleAttrPattern = Pattern.compile("@(\\w+)=['\"](.*?)['\"]")
                    val simpleAttrMatcher = simpleAttrPattern.matcher(predicate)
                    if (simpleAttrMatcher.matches()) {
                        val attr = simpleAttrMatcher.group(1)
                        val value = simpleAttrMatcher.group(2)
                        matchesAttribute(node, attr, value, "==")
                    } else {
                        false // 不支持的谓词格式
                    }
                }
            }
        }
    }
}

/**
 * 评估位置谓词
 * @param predicate 谓词表达式
 * @param index 当前索引
 * @return 是否匹配位置谓词
 */
private fun evaluatePositionPredicate(predicate: String, index: Int): Boolean {
    // position() = n
    if (predicate.contains("=")) {
        val position = predicate.substringAfter("=").trim().toIntOrNull() ?: return false
        return index + 1 == position // XPath位置从1开始
    }
    // position() > n
    else if (predicate.contains(">")) {
        val position = predicate.substringAfter(">").trim().toIntOrNull() ?: return false
        return index + 1 > position
    }
    // position() < n
    else if (predicate.contains("<")) {
        val position = predicate.substringAfter("<").trim().toIntOrNull() ?: return false
        return index + 1 < position
    }
    return false
}

/**
 * 评估last()函数谓词
 * @param predicate 谓词表达式
 * @param index 当前索引
 * @param totalCount 节点总数
 * @return 是否匹配last()谓词
 */
private fun evaluateLastPredicate(predicate: String, index: Int, totalCount: Int): Boolean {
    // last()
    if (predicate == "last()") {
        return index == totalCount - 1
    }
    // last() - n
    else if (predicate.contains("-")) {
        val offset = predicate.substringAfter("-").trim().toIntOrNull() ?: return false
        return index == totalCount - 1 - offset
    }
    return false
}

/**
 * 检查节点属性是否匹配指定值和操作符
 * @param node 要检查的节点
 * @param attr 属性名
 * @param value 期望值
 * @param operator 操作符（==, !=, ~=, ^=, $=, ?=）
 * @return 是否匹配
 */
private fun matchesAttribute(node: AccessibilityNodeInfo, attr: String, value: String, operator: String = "=="): Boolean {
    val attrValue = when (attr.lowercase()) {
        "text" -> node.text?.toString()
        "id" -> node.viewIdResourceName
        "desc" -> node.contentDescription?.toString()
        "pkg" -> node.packageName?.toString()
        "cls" -> node.className?.toString()
        "clickable" -> node.isClickable.toString()
        "scrollable" -> node.isScrollable.toString()
        "checked" -> node.isChecked.toString()
        "selected" -> node.isSelected.toString()
        "enabled" -> node.isEnabled.toString()
        "focused" -> node.isFocused.toString()
        "visible" -> (!node.isVisibleToUser).toString()
        else -> null // 不支持的属性
    } ?: return false

    return when (operator) {
        "==" -> attrValue == value
        "!=" -> attrValue != value
        "~=" -> attrValue.contains(value)
        "^=" -> attrValue.startsWith(value)
        "$=" -> attrValue.endsWith(value)
        "?=" -> attrValue.matches(Regex(value))
        else -> false // 不支持的操作符
    }
}

/**
 * 便捷方法：查找单个节点
 * @param xpath XPath表达式
 * @return 找到的第一个节点，如果没找到则返回null
 */
fun AccessibilityNodeInfo.findNodeByXPath(xpath: String): AccessibilityNodeInfo? {
    return findNodesByXPath(xpath).firstOrNull()
}

/**
 * 便捷方法：检查是否存在匹配的节点
 * @param xpath XPath表达式
 * @return 是否存在匹配节点
 */
fun AccessibilityNodeInfo.existsByXPath(xpath: String): Boolean {
    val nodes = findNodesByXPath(xpath)
    val exists = nodes.isNotEmpty()
    nodes.forEach { it.recycle() } // 回收节点
    return exists
}

/**
 * 便捷方法：点击匹配的第一个节点
 * @param xpath XPath表达式
 * @return 是否成功点击
 */
fun AccessibilityNodeInfo.clickByXPath(xpath: String): Boolean {
    val node = findNodeByXPath(xpath) ?: return false
    val result = node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
    node.recycle()
    return result
} 