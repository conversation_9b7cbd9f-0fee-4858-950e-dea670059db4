package com.coffee.chatbot

import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.accessibility.AccessibilityManager
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.coffee.chatbot.service.FloatingWindowService
import com.coffee.chatbot.ui.theme.ChatbotTheme

class MainActivity : ComponentActivity() {
    
    private var isServiceRunning = false
    
    private var overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (Settings.canDrawOverlays(this)) {
            // 如果服务尚未启动，则检查无障碍服务权限
            if (!isServiceRunning) {
                checkAccessibilityPermission()
            }
        } else {
            Toast.makeText(this, "需要悬浮窗权限才能显示浮动按钮", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ChatbotTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    MainContent(
                        modifier = Modifier.padding(innerPadding),
                        serviceRunning = isServiceRunning,
                        onStartService = {
                            checkAndRequestPermissions()
                        },
                        onStopService = {
                            stopFloatingWindowService()
                            isServiceRunning = false
                        }
                    )
                }
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        // 检查服务是否正在运行并更新UI状态
        isServiceRunning = isFloatingServiceRunning()
        updateUI()
    }
    
    private fun updateUI() {
        setContent {
            ChatbotTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    MainContent(
                        modifier = Modifier.padding(innerPadding),
                        serviceRunning = isServiceRunning,
                        onStartService = {
                            checkAndRequestPermissions()
                        },
                        onStopService = {
                            stopFloatingWindowService()
                            isServiceRunning = false
                        }
                    )
                }
            }
        }
    }
    
    private fun isFloatingServiceRunning(): Boolean {
        // 简单的检查方法，在真实应用中可能需要更复杂的检查
        return Settings.canDrawOverlays(this) && isAccessibilityServiceEnabled()
    }
    
    private fun checkAndRequestPermissions() {
        // 检查悬浮窗权限
        if (!Settings.canDrawOverlays(this)) {
            requestOverlayPermission()
            return
        }
        
        // 检查无障碍服务权限
        checkAccessibilityPermission()
    }
    
    private fun checkAccessibilityPermission() {
        if (!isAccessibilityServiceEnabled()) {
            requestAccessibilityPermission()
            return
        }
        
        // 所有权限都已授予，启动服务
        startFloatingWindowService()
    }
    
    private fun requestOverlayPermission() {
        val intent = Intent(
            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            Uri.parse("package:$packageName")
        )
        Toast.makeText(
            this,
            "请启用悬浮窗权限以显示浮动按钮",
            Toast.LENGTH_LONG
        ).show()
        overlayPermissionLauncher.launch(intent)
    }
    
    private fun requestAccessibilityPermission() {
        Toast.makeText(
            this,
            "请启用无障碍服务以提取聊天内容",
            Toast.LENGTH_LONG
        ).show()
        startActivity(Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS))
    }
    
    private fun isAccessibilityServiceEnabled(): Boolean {
        val accessibilityManager = getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        val enabledServices = accessibilityManager.getEnabledAccessibilityServiceList(
            AccessibilityServiceInfo.FEEDBACK_ALL_MASK
        )
        
        for (service in enabledServices) {
            if (service.id.contains(packageName)) {
                return true
            }
        }
        
        return false
    }
    
    private fun startFloatingWindowService() {
        val intent = Intent(this, FloatingWindowService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
        
        isServiceRunning = true
        updateUI()
        
        Toast.makeText(
            this,
            "浮动按钮服务已启动，现在可以导航到聊天应用了。",
            Toast.LENGTH_LONG
        ).show()
    }
    
    private fun stopFloatingWindowService() {
        val intent = Intent(this, FloatingWindowService::class.java)
        intent.action = "STOP_SERVICE"
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
        
        isServiceRunning = false
        updateUI()
        
        Toast.makeText(this, "浮动按钮服务已停止", Toast.LENGTH_SHORT).show()
    }
}

@Composable
fun MainContent(
    modifier: Modifier = Modifier, 
    serviceRunning: Boolean = false,
    onStartService: () -> Unit,
    onStopService: () -> Unit
) {
    Column(
        modifier = modifier.fillMaxSize().padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "聊天提取应用",
            fontSize = 24.sp,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        Text(
            text = "此应用创建一个浮动按钮，使您能够从其他消息应用中提取聊天历史记录。",
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        Text(
            text = "应用需要悬浮窗权限和无障碍服务才能正常运行。",
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        if (!serviceRunning) {
            Button(
                onClick = onStartService,
                modifier = Modifier
                    .height(50.dp)
                    .width(200.dp)
            ) {
                Text("启动服务")
            }
        } else {
            Button(
                onClick = onStopService,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                ),
                modifier = Modifier
                    .height(50.dp)
                    .width(200.dp)
            ) {
                Text("停止服务")
            }
        }
    }
}