package com.coffee.chatbot.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.graphics.*
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityNodeInfo

/**
 * 客服接待页面处理器 - 纯 Accessibility Services 实现
 *
 * 功能：
 * 1. 检测是否在客服接待页面
 * 2. 查找有未读消息的会话
 * 3. 点击进入会话
 * 4. 提供详细的调试日志
 */
class CustomerServiceListHandler(private val context: Context) {

    companion object {
        private const val TAG = "CustomerServiceHandler"

        // 目标应用包名
        private const val TARGET_PACKAGE = "com.xingin.eva"

        // 点击标记持续时间
        private const val CLICK_MARKER_DURATION = 2000L
    }

    private var accessibilityService: AccessibilityService? = null
    private val handler = Handler(Looper.getMainLooper())
    private var showClickMarker = true
    private var markerView: View? = null
    private val windowManager by lazy { context.getSystemService(Context.WINDOW_SERVICE) as WindowManager }

    // 设置 AccessibilityService 实例
    fun setAccessibilityService(service: AccessibilityService) {
        this.accessibilityService = service
        Log.d(TAG, "✅ AccessibilityService 已设置")
    }

    /**
     * 主要入口方法：检查并点击有未读消息的会话
     */
    fun checkAndEnterNewMessage(): Boolean {
        Log.d(TAG, "🚀 === 开始检查未读消息 ===")

        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return false
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return false
        }

        try {
            // 验证是否在目标应用
            if (!isTargetApp(rootNode)) {
                Log.w(TAG, "❌ 不在目标应用中")
                return false
            }

            // 检查是否在客服接待页面
            if (!isInCustomerServicePage(rootNode)) {
                Log.w(TAG, "❌ 不在客服接待页面")
                return false
            }

            Log.d(TAG, "✅ 确认在客服接待页面")

            // 查找有未读消息的会话并点击
            val result = findAndClickUnreadConversation(rootNode)
            Log.d(TAG, "🚀 === 检查未读消息完成，结果: $result ===")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "检查未读消息时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 验证是否在目标应用
     */
    private fun isTargetApp(rootNode: AccessibilityNodeInfo): Boolean {
        val packageName = rootNode.packageName?.toString()
        Log.d(TAG, "当前应用包名: $packageName")
        return packageName == TARGET_PACKAGE
    }

    /**
     * 检查是否在客服接待页面
     */
    private fun isInCustomerServicePage(rootNode: AccessibilityNodeInfo): Boolean {
        val customerServiceNodes = findNodesByText(rootNode, "客服接待")
        Log.d(TAG, "找到 ${customerServiceNodes.size} 个'客服接待'文本节点")
        return customerServiceNodes.isNotEmpty()
    }

    /**
     * 查找并点击有未读消息的会话
     */
    private fun findAndClickUnreadConversation(rootNode: AccessibilityNodeInfo): Boolean {
        // 查找所有可能的会话容器
        val conversationContainers = findConversationContainers(rootNode)
        Log.d(TAG, "找到 ${conversationContainers.size} 个会话容器")

        if (conversationContainers.isEmpty()) {
            Log.w(TAG, "❌ 未找到会话容器")
            return false
        }

        // 遍历每个容器，查找有未读消息的会话
        for ((index, container) in conversationContainers.withIndex()) {
            Log.d(TAG, "--- 检查容器 $index ---")
            val conversations = getConversationsFromContainer(container)
            Log.d(TAG, "容器 $index 中找到 ${conversations.size} 个会话")

            for ((convIndex, conversation) in conversations.withIndex()) {
                Log.d(TAG, "🔍 检查会话 $convIndex")

                if (hasUnreadMessage(conversation)) {
                    Log.d(TAG, "🎯 会话 $convIndex 有未读消息，准备点击")
                    return clickConversation(conversation, convIndex)
                } else {
                    Log.d(TAG, "⭕ 会话 $convIndex 无未读消息")
                }
            }
        }

        Log.d(TAG, "❌ 未找到有未读消息的会话")
        return false
    }

    /**
     * 查找会话容器（ScrollView 或 RecyclerView）
     */
    private fun findConversationContainers(rootNode: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val containers = mutableListOf<AccessibilityNodeInfo>()
        findContainersRecursive(rootNode, containers)
        return containers
    }

    private fun findContainersRecursive(node: AccessibilityNodeInfo, containers: MutableList<AccessibilityNodeInfo>) {
        val className = node.className?.toString()

        // 查找可滚动的容器
        if (className == "android.widget.ScrollView" ||
            className == "androidx.recyclerview.widget.RecyclerView" ||
            className == "android.widget.ListView" ||
            node.isScrollable) {
            containers.add(node)
            Log.d(TAG, "找到容器: $className, bounds: ${getBounds(node)}")
        }

        // 递归搜索子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findContainersRecursive(child, containers)
                child.recycle()
            }
        }
    }

    /**
     * 从容器中获取会话列表
     */
    private fun getConversationsFromContainer(container: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val conversations = mutableListOf<AccessibilityNodeInfo>()

        Log.d(TAG, "分析容器: ${container.className}, childCount: ${container.childCount}")

        // 如果是ScrollView，需要找到其内部的ViewGroup容器
        if (container.className?.toString() == "android.widget.ScrollView") {
            if (container.childCount > 0) {
                val innerContainer = container.getChild(0)
                if (innerContainer != null) {
                    Log.d(TAG, "ScrollView内部容器: ${innerContainer.className}, childCount: ${innerContainer.childCount}")
                    collectConversations(innerContainer, conversations)
                    innerContainer.recycle()
                }
            }
        } else {
            // 直接从容器中收集会话
            collectConversations(container, conversations)
        }

        Log.d(TAG, "从容器中找到 ${conversations.size} 个会话")
        return conversations
    }

    /**
     * 递归收集会话项
     */
    private fun collectConversations(node: AccessibilityNodeInfo, conversations: MutableList<AccessibilityNodeInfo>) {
        // 检查当前节点是否是会话项
        if (isConversationItem(node)) {
            conversations.add(node)
            Log.d(TAG, "找到会话项: bounds=${getBounds(node)}")
            return // 不再递归，避免重复
        }

        // 递归搜索子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                collectConversations(child, conversations)
                // 注意：不要在这里回收child，因为它可能被添加到conversations列表中
            }
        }
    }

    /**
     * 判断节点是否是会话项
     */
    private fun isConversationItem(node: AccessibilityNodeInfo): Boolean {
        // 会话项通常是可点击的ViewGroup，包含多个子元素
        val isViewGroup = node.className?.toString() == "android.view.ViewGroup"
        val hasChildren = node.childCount > 2 // 至少包含昵称、时间、内容等
        val isClickable = node.isClickable

        // 检查是否包含时间信息（会话项的特征）
        val hasTimeInfo = containsTimeInfo(node)

        val result = isViewGroup && hasChildren && (isClickable || hasTimeInfo)

        if (result) {
            Log.d(TAG, "识别为会话项: className=${node.className}, childCount=${node.childCount}, clickable=$isClickable, hasTime=$hasTimeInfo")
        }

        return result
    }

    /**
     * 检查节点是否包含时间信息
     */
    private fun containsTimeInfo(node: AccessibilityNodeInfo): Boolean {
        val timePattern = Regex("\\d{1,2}:\\d{2}|刚刚|昨天|星期|周|月|日|分钟前|小时前|天前")

        // 检查当前节点文本
        node.text?.toString()?.let { text ->
            if (timePattern.containsMatchIn(text)) {
                return true
            }
        }

        // 检查子节点文本
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            child?.text?.toString()?.let { text ->
                if (timePattern.containsMatchIn(text)) {
                    child.recycle()
                    return true
                }
            }
            child?.recycle()
        }

        return false
    }

    /**
     * 检查会话是否有未读消息
     */
    private fun hasUnreadMessage(conversation: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "--- 检查未读消息 ---")

        // 查找未读消息数字标记
        val unreadCount = findUnreadCount(conversation)
        if (unreadCount > 0) {
            Log.d(TAG, "✅ 找到未读消息数: $unreadCount")
            return true
        }

        // 查找"已等待"文本（表示有新消息等待回复）
        val hasWaitingText = findWaitingText(conversation)
        if (hasWaitingText) {
            Log.d(TAG, "✅ 找到等待回复文本")
            return true
        }

        // 检查是否有红点或其他未读标记
        val hasUnreadIndicator = findUnreadIndicator(conversation)
        if (hasUnreadIndicator) {
            Log.d(TAG, "✅ 找到未读标记")
            return true
        }

        Log.d(TAG, "❌ 未找到未读消息标记")
        return false
    }

    /**
     * 查找未读消息数量
     */
    private fun findUnreadCount(node: AccessibilityNodeInfo): Int {
        // 递归查找数字文本
        return findUnreadCountRecursive(node)
    }

    private fun findUnreadCountRecursive(node: AccessibilityNodeInfo): Int {
        // 检查当前节点文本是否是数字
        node.text?.toString()?.let { text ->
            val trimmed = text.trim()
            if (trimmed.matches(Regex("\\d+"))) {
                val count = trimmed.toIntOrNull() ?: 0
                if (count > 0) {
                    Log.d(TAG, "找到未读消息数: $count")
                    return count
                }
            }
        }

        // 递归检查子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val count = findUnreadCountRecursive(child)
                child.recycle()
                if (count > 0) {
                    return count
                }
            }
        }

        return 0
    }

    /**
     * 查找"已等待"文本
     */
    private fun findWaitingText(node: AccessibilityNodeInfo): Boolean {
        return findTextRecursive(node, "已等待")
    }

    /**
     * 查找未读标记
     */
    private fun findUnreadIndicator(node: AccessibilityNodeInfo): Boolean {
        // 可以根据实际情况添加更多未读标记的检测逻辑
        return false
    }

    /**
     * 点击会话
     */
    private fun clickConversation(conversation: AccessibilityNodeInfo, index: Int): Boolean {
        val service = accessibilityService ?: return false

        val bounds = Rect()
        conversation.getBoundsInScreen(bounds)

        val centerX = bounds.centerX()
        val centerY = bounds.centerY()

        Log.d(TAG, "准备点击会话 $index: bounds=$bounds, center=($centerX, $centerY)")

        // 显示点击标记
        showClickMarker(centerX, centerY)

        // 执行点击手势
        return performClick(centerX, centerY)
    }

    /**
     * 执行点击手势
     */
    private fun performClick(x: Int, y: Int): Boolean {
        val service = accessibilityService ?: return false

        Log.d(TAG, "执行点击手势: ($x, $y)")

        val path = Path().apply {
            moveTo(x.toFloat(), y.toFloat())
        }

        val gestureBuilder = GestureDescription.Builder()
        val strokeDescription = GestureDescription.StrokeDescription(path, 0, 100)
        gestureBuilder.addStroke(strokeDescription)

        val gesture = gestureBuilder.build()

        return service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
            override fun onCompleted(gestureDescription: GestureDescription?) {
                Log.d(TAG, "✅ 点击手势执行完成: ($x, $y)")
            }

            override fun onCancelled(gestureDescription: GestureDescription?) {
                Log.w(TAG, "❌ 点击手势被取消: ($x, $y)")
            }
        }, null)
    }



    /**
     * 详细分析会话项内容
     */
    private fun analyzeConversationItem(conversationItem: AccessibilityNodeInfo, index: Int) {
        Log.d(TAG, "🔍 === 分析会话项 $index 详细信息 ===")

        // 查找所有TextView节点
        val textViews = mutableListOf<AccessibilityNodeInfo>()
        findAllTextViews(conversationItem, textViews)

        Log.d(TAG, "会话项 $index 包含 ${textViews.size} 个TextView:")

        var unreadCount: String? = null
        var nickname: String? = null
        var messageContent: String? = null
        var messageTime: String? = null
        var waitingTime: String? = null

        textViews.forEachIndexed { tvIndex, textView ->
            val text = textView.text?.toString() ?: ""
            val bounds = Rect()
            textView.getBoundsInScreen(bounds)

            Log.d(TAG, "  TextView[$tvIndex]: text='$text', bounds=$bounds, size=${bounds.width()}x${bounds.height()}")

            // 分类识别文本内容
            when {
                // 未读消息数：纯数字且尺寸小
                text.matches(Regex("\\d+")) && bounds.width() < 50 && bounds.height() < 50 -> {
                    unreadCount = text
                    Log.d(TAG, "    ✅ 识别为未读消息数: $text")
                }
                // 时间格式：包含时间相关字符
                text.contains("刚刚") || text.contains(":") || text.contains("-") && text.length > 5 -> {
                    if (messageTime == null) {
                        messageTime = text
                        Log.d(TAG, "    ✅ 识别为消息时间: $text")
                    }
                }
                // 等待时间：包含"等待"或"秒"
                text.contains("等待") || text.contains("秒") -> {
                    waitingTime = text
                    Log.d(TAG, "    ✅ 识别为等待时间: $text")
                }
                // 昵称：位置在左上区域且长度适中
                bounds.left < 400 && bounds.top < bounds.bottom - 50 && text.length in 2..20 &&
                nickname == null && !text.contains("等待") -> {
                    nickname = text
                    Log.d(TAG, "    ✅ 识别为昵称: $text")
                }
                // 消息内容：其他较长文本
                text.length > 2 && messageContent == null &&
                !text.contains("等待") && !text.contains("刚刚") && !text.contains(":") -> {
                    messageContent = text
                    Log.d(TAG, "    ✅ 识别为消息内容: $text")
                }
                else -> {
                    Log.d(TAG, "    ❓ 未分类文本: $text")
                }
            }
        }

        // 汇总输出
        Log.d(TAG, "📋 会话项 $index 汇总信息:")
        Log.d(TAG, "  未读消息数: ${unreadCount ?: "无"}")
        Log.d(TAG, "  昵称: ${nickname ?: "未识别"}")
        Log.d(TAG, "  消息内容: ${messageContent ?: "未识别"}")
        Log.d(TAG, "  消息时间: ${messageTime ?: "未识别"}")
        Log.d(TAG, "  等待时间: ${waitingTime ?: "无"}")
        Log.d(TAG, "  是否有未读消息: ${unreadCount != null}")
        Log.d(TAG, "🔍 === 会话项 $index 分析完成 ===\n")
    }

    /**
     * 查找所有TextView节点
     */
    private fun findAllTextViews(node: AccessibilityNodeInfo, result: MutableList<AccessibilityNodeInfo>) {
        if (node.className?.toString() == "android.widget.TextView") {
            result.add(node)
        }

        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findAllTextViews(child, result)
            }
        }
    }



    /**
     * 查找未读消息角标
     * 对应XML第84行：<node index="0" text="1" ... class="android.widget.TextView"
     */
    private fun findUnreadMessageBadge(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        return findUnreadMessageBadgeRecursive(node, 0)
    }

    private fun findUnreadMessageBadgeRecursive(node: AccessibilityNodeInfo, depth: Int): AccessibilityNodeInfo? {
        val indent = "  ".repeat(depth)

        // 检查当前节点是否是未读消息角标
        if (node.className?.toString() == "android.widget.TextView") {
            val text = node.text?.toString()
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            Log.d(TAG, "${indent}检查TextView: text='$text', bounds=$bounds, size=${bounds.width()}x${bounds.height()}")

            if (text != null && text.matches(Regex("\\d+"))) {
                Log.d(TAG, "${indent}  -> 文本是数字: $text")

                // 进一步验证：角标通常位置较小且在头像区域
                if (bounds.width() < 50 && bounds.height() < 50) {
                    Log.d(TAG, "${indent}  -> ✅ 尺寸符合角标特征 (${bounds.width()}x${bounds.height()})")
                    Log.d(TAG, "🎯 找到未读消息角标: text='$text', bounds=$bounds")
                    return node
                } else {
                    Log.d(TAG, "${indent}  -> ❌ 尺寸过大，不是角标 (${bounds.width()}x${bounds.height()})")
                }
            } else {
                Log.d(TAG, "${indent}  -> 文本不是纯数字: '$text'")
            }
        } else {
            Log.d(TAG, "${indent}节点类型: ${node.className}, childCount: ${node.childCount}")
        }

        // 递归查找子节点
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                val result = findUnreadMessageBadgeRecursive(child, depth + 1)
                if (result != null) return result
            }
        }

        return null
    }

    /**
     * 点击会话项
     * 基于XML分析，会话项的可点击区域在第79行和第105行：
     * clickable="true" bounds="[0,642][1080,858]"
     */
    private fun clickConversationItem(conversationItem: AccessibilityNodeInfo, index: Int): Boolean {
        // 查找可点击的子节点
        val clickableNode = findClickableNode(conversationItem)
        if (clickableNode == null) {
            Log.w(TAG, "会话项 $index 未找到可点击节点")
            return false
        }
        
        val bounds = Rect()
        clickableNode.getBoundsInScreen(bounds)
        
        // 获取会话项整体区域用于对比
        val parentBounds = Rect()
        conversationItem.getBoundsInScreen(parentBounds)

        Log.d(TAG, "可点击节点bounds: $bounds")
        Log.d(TAG, "会话项整体bounds: $parentBounds")
        Log.d(TAG, "可点击区域尺寸: ${bounds.width()}x${bounds.height()}")
        Log.d(TAG, "会话项整体尺寸: ${parentBounds.width()}x${parentBounds.height()}")

        // 强制使用会话项整体区域，避免点击小的可点击区域导致位置偏右
        val targetBounds = if (parentBounds.width() > 500) {  // 整体区域通常很宽
            Log.d(TAG, "✅ 使用会话项整体区域进行点击")
            parentBounds
        } else {
            Log.d(TAG, "⚠️ 会话项整体区域异常，使用可点击节点区域")
            bounds
        }

        // 计算点击位置：避开左侧头像区域，点击在会话项的上半部分
        // X坐标：40%位置，避开头像和右侧信息
        // Y坐标：30%位置，点击在昵称和消息内容区域，避免点击到底部
        val clickX = targetBounds.left + (targetBounds.width() * 0.4).toInt()  // 40%位置
        val clickY = targetBounds.top + (targetBounds.height() * 0.3).toInt()   // 30%位置，在上半部分

        Log.d(TAG, "最终点击位置: ($clickX, $clickY)")

        // 调试点击位置计算
        debugClickPosition(targetBounds, clickX, clickY)
        
        // 显示点击标记（需要在主线程执行）
        if (showClickMarker) {
            try {
                // 使用Handler.post确保在主线程执行
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    showClickMarker(clickX, clickY)
                }
            } catch (e: Exception) {
                Log.w(TAG, "显示点击标记失败: ${e.message}")
            }
        }

        // 执行手势点击
        Log.d(TAG, "开始执行手势点击...")
        val gestureResult = performGestureClick(clickX, clickY)

        if (!gestureResult) {
            Log.w(TAG, "手势点击失败，尝试备用方法...")
            // 备用方法：尝试直接点击节点
            return tryDirectNodeClick(conversationItem, clickableNode)
        }

        return gestureResult
    }

    /**
     * 查找可点击的节点
     * 对应XML第79行和第105行的clickable="true"节点
     */
    private fun findClickableNode(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        if (node.isClickable) {
            return node
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                val result = findClickableNode(child)
                if (result != null) return result
            }
        }
        
        return null
    }

    /**
     * 执行手势点击 - 增强版
     */
    private fun performGestureClick(x: Int, y: Int): Boolean {
        try {
            Log.d(TAG, "准备执行手势点击: ($x, $y)")

            // 验证坐标是否合理
            val service = accessibilityService ?: return false
            val screenWidth = service.resources.displayMetrics.widthPixels
            val screenHeight = service.resources.displayMetrics.heightPixels

            if (x < 0 || x > screenWidth || y < 0 || y > screenHeight) {
                Log.w(TAG, "❌ 点击坐标超出屏幕范围: ($x, $y), 屏幕尺寸: ${screenWidth}x${screenHeight}")
                return false
            }

            Log.d(TAG, "✅ 坐标验证通过，屏幕尺寸: ${screenWidth}x${screenHeight}")

            // 检查无障碍服务状态
            try {
                val flags = service.serviceInfo?.flags ?: 0
                Log.d(TAG, "无障碍服务标志: $flags")
            } catch (e: Exception) {
                Log.w(TAG, "检查无障碍服务状态失败", e)
            }

            // 尝试多种手势策略
            return tryMultipleGestureStrategies(x, y)

        } catch (e: Exception) {
            Log.e(TAG, "执行手势点击失败: ($x, $y)", e)
            return false
        }
    }

    /**
     * 尝试多种手势策略
     */
    private fun tryMultipleGestureStrategies(x: Int, y: Int): Boolean {
        Log.d(TAG, "尝试多种手势策略...")

        // 策略1：标准点击手势
        if (tryStandardClick(x, y)) {
            Log.i(TAG, "✅ 标准点击手势成功")
            return true
        }

        // 策略2：长按手势
        Log.d(TAG, "标准点击失败，尝试长按手势...")
        if (tryLongPress(x, y)) {
            Log.i(TAG, "✅ 长按手势成功")
            return true
        }

        // 策略3：双击手势
        Log.d(TAG, "长按失败，尝试双击手势...")
        if (tryDoubleClick(x, y)) {
            Log.i(TAG, "✅ 双击手势成功")
            return true
        }

        Log.w(TAG, "❌ 所有手势策略都失败了")
        return false
    }

    /**
     * 标准点击手势
     */
    private fun tryStandardClick(x: Int, y: Int): Boolean {
        return try {
            Log.d(TAG, "执行标准点击手势: ($x, $y)")

            val service = accessibilityService ?: return false

            val path = Path().apply {
                moveTo(x.toFloat(), y.toFloat())
            }

            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 100)
            val gestureDescription = GestureDescription.Builder()
                .addStroke(strokeDescription)
                .build()

            var gestureCompleted = false
            var gestureCancelled = false

            val result = service.dispatchGesture(
                gestureDescription,
                object : AccessibilityService.GestureResultCallback() {
                    override fun onCompleted(gestureDescription: GestureDescription?) {
                        Log.i(TAG, "标准点击手势完成: ($x, $y)")
                        gestureCompleted = true
                    }

                    override fun onCancelled(gestureDescription: GestureDescription?) {
                        Log.w(TAG, "标准点击手势被取消: ($x, $y)")
                        gestureCancelled = true
                    }
                },
                null
            )

            Log.d(TAG, "标准点击分发结果: $result")

            // 等待手势完成
            Thread.sleep(200)

            if (gestureCancelled) {
                Log.w(TAG, "标准点击被取消")
                return false
            }

            result

        } catch (e: Exception) {
            Log.e(TAG, "标准点击手势异常", e)
            false
        }
    }

    /**
     * 长按手势
     */
    private fun tryLongPress(x: Int, y: Int): Boolean {
        return try {
            Log.d(TAG, "执行长按手势: ($x, $y)")

            val service = accessibilityService ?: return false

            val path = Path().apply {
                moveTo(x.toFloat(), y.toFloat())
            }

            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 300) // 长按300ms
            val gestureDescription = GestureDescription.Builder()
                .addStroke(strokeDescription)
                .build()

            val result = service.dispatchGesture(
                gestureDescription,
                object : AccessibilityService.GestureResultCallback() {
                    override fun onCompleted(gestureDescription: GestureDescription?) {
                        Log.i(TAG, "长按手势完成: ($x, $y)")
                    }

                    override fun onCancelled(gestureDescription: GestureDescription?) {
                        Log.w(TAG, "长按手势被取消: ($x, $y)")
                    }
                },
                null
            )

            Log.d(TAG, "长按分发结果: $result")
            Thread.sleep(400)
            result

        } catch (e: Exception) {
            Log.e(TAG, "长按手势异常", e)
            false
        }
    }

    /**
     * 双击手势
     */
    private fun tryDoubleClick(x: Int, y: Int): Boolean {
        return try {
            Log.d(TAG, "执行双击手势: ($x, $y)")

            val service = accessibilityService ?: return false

            // 第一次点击
            val path1 = Path().apply {
                moveTo(x.toFloat(), y.toFloat())
            }
            val stroke1 = GestureDescription.StrokeDescription(path1, 0, 50)

            // 第二次点击
            val path2 = Path().apply {
                moveTo(x.toFloat(), y.toFloat())
            }
            val stroke2 = GestureDescription.StrokeDescription(path2, 100, 50)

            val gestureDescription = GestureDescription.Builder()
                .addStroke(stroke1)
                .addStroke(stroke2)
                .build()

            val result = service.dispatchGesture(
                gestureDescription,
                object : AccessibilityService.GestureResultCallback() {
                    override fun onCompleted(gestureDescription: GestureDescription?) {
                        Log.i(TAG, "双击手势完成: ($x, $y)")
                    }

                    override fun onCancelled(gestureDescription: GestureDescription?) {
                        Log.w(TAG, "双击手势被取消: ($x, $y)")
                    }
                },
                null
            )

            Log.d(TAG, "双击分发结果: $result")
            Thread.sleep(300)
            result

        } catch (e: Exception) {
            Log.e(TAG, "双击手势异常", e)
            false
        }
    }

    /**
     * 备用方法：直接点击节点
     */
    private fun tryDirectNodeClick(conversationItem: AccessibilityNodeInfo, clickableNode: AccessibilityNodeInfo?): Boolean {
        return try {
            Log.d(TAG, "尝试直接节点点击...")

            // 方法1：尝试点击可点击节点
            if (clickableNode != null && clickableNode.isClickable) {
                Log.d(TAG, "尝试点击可点击节点")
                val result1 = clickableNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                Log.d(TAG, "可点击节点点击结果: $result1")
                if (result1) {
                    Log.i(TAG, "✅ 直接点击可点击节点成功")
                    return true
                }
            }

            // 方法2：尝试点击会话项整体
            Log.d(TAG, "尝试点击会话项整体")
            val result2 = conversationItem.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            Log.d(TAG, "会话项整体点击结果: $result2")
            if (result2) {
                Log.i(TAG, "✅ 直接点击会话项整体成功")
                return true
            }

            Log.w(TAG, "❌ 所有直接点击方法都失败了")
            false

        } catch (e: Exception) {
            Log.e(TAG, "直接节点点击异常", e)
            false
        }
    }

    /**
     * 显示全局点击标记
     */
    private fun showClickMarker(x: Int, y: Int) {
        try {
            hideClickMarker() // 先隐藏之前的标记

            // 创建红色圆形标记
            val marker = object : View(context) {
                override fun onDraw(canvas: Canvas) {
                    super.onDraw(canvas)

                    // 红色填充圆
                    val redPaint = Paint().apply {
                        color = Color.RED
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawCircle(25f, 25f, 20f, redPaint)

                    // 白色边框
                    val whitePaint = Paint().apply {
                        color = Color.WHITE
                        style = Paint.Style.STROKE
                        strokeWidth = 4f
                        isAntiAlias = true
                    }
                    canvas.drawCircle(25f, 25f, 20f, whitePaint)
                }
            }

            // 设置悬浮窗参数
            val params = WindowManager.LayoutParams().apply {
                type = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
                format = PixelFormat.TRANSLUCENT
                width = 50
                height = 50
                gravity = Gravity.TOP or Gravity.START
                this.x = x - 25
                this.y = y - 25
            }

            windowManager.addView(marker, params)
            markerView = marker

            Log.d(TAG, "显示点击标记: ($x, $y)")

            // 2秒后自动隐藏
            handler.postDelayed({ hideClickMarker() }, 2000)

        } catch (e: Exception) {
            Log.e(TAG, "显示点击标记失败", e)
        }
    }

    /**
     * 隐藏点击标记
     */
    private fun hideClickMarker() {
        try {
            markerView?.let { view ->
                windowManager.removeView(view)
                markerView = null
                Log.d(TAG, "隐藏点击标记")
            }
        } catch (e: Exception) {
            Log.e(TAG, "隐藏点击标记失败", e)
            markerView = null
        }
    }

    /**
     * 设置是否显示点击标记
     */
    fun setShowClickMarker(show: Boolean) {
        this.showClickMarker = show
        if (!show) {
            hideClickMarker()
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        hideClickMarker()
        handler.removeCallbacksAndMessages(null)
        Log.d(TAG, "资源清理完成")
    }

    // ===== 辅助工具方法 =====

    /**
     * 根据文本查找节点
     */
    private fun findNodesByText(rootNode: AccessibilityNodeInfo, text: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        findNodesByTextRecursive(rootNode, text, result)
        return result
    }

    private fun findNodesByTextRecursive(node: AccessibilityNodeInfo, text: String, result: MutableList<AccessibilityNodeInfo>) {
        if (node.text?.toString()?.contains(text) == true) {
            result.add(node)
        }
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findNodesByTextRecursive(child, text, result)
                child.recycle()
            }
        }
    }

    /**
     * 递归查找文本
     */
    private fun findTextRecursive(node: AccessibilityNodeInfo, targetText: String): Boolean {
        // 检查当前节点文本
        node.text?.toString()?.let { text ->
            if (text.contains(targetText)) {
                return true
            }
        }

        // 递归检查子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val found = findTextRecursive(child, targetText)
                child.recycle()
                if (found) {
                    return true
                }
            }
        }

        return false
    }

    /**
     * 获取节点边界信息
     */
    private fun getBounds(node: AccessibilityNodeInfo): String {
        val bounds = Rect()
        node.getBoundsInScreen(bounds)
        return "[${bounds.left},${bounds.top}][${bounds.right},${bounds.bottom}]"
    }

    /**
     * 调试点击位置计算
     */
    private fun debugClickPosition(bounds: Rect, clickX: Int, clickY: Int) {
        Log.d(TAG, "=== 点击位置调试信息 ===")
        Log.d(TAG, "目标区域: $bounds")
        Log.d(TAG, "区域尺寸: ${bounds.width()}x${bounds.height()}")
        Log.d(TAG, "区域中心: (${bounds.centerX()}, ${bounds.centerY()})")
        Log.d(TAG, "计算的点击位置: ($clickX, $clickY)")

        // 验证点击位置是否在区域内
        val inBounds = clickX >= bounds.left && clickX <= bounds.right &&
                      clickY >= bounds.top && clickY <= bounds.bottom
        Log.d(TAG, "点击位置是否在区域内: $inBounds")

        if (!inBounds) {
            Log.w(TAG, "❌ 点击位置超出目标区域！")
            Log.w(TAG, "X坐标: $clickX, 应在[${bounds.left}, ${bounds.right}]")
            Log.w(TAG, "Y坐标: $clickY, 应在[${bounds.top}, ${bounds.bottom}]")
        } else {
            Log.d(TAG, "✅ 点击位置在目标区域内")
        }

        // 计算点击位置相对于区域的百分比
        val xPercent = ((clickX - bounds.left).toFloat() / bounds.width() * 100).toInt()
        val yPercent = ((clickY - bounds.top).toFloat() / bounds.height() * 100).toInt()
        Log.d(TAG, "点击位置百分比: X=${xPercent}%, Y=${yPercent}%")
        Log.d(TAG, "=== 调试信息结束 ===")
    }

    /**
     * 转储节点结构用于调试
     */
    private fun dumpNodeStructure(node: AccessibilityNodeInfo?, title: String, maxDepth: Int = 3) {
        if (node == null) {
            Log.d(TAG, "$title: node为null")
            return
        }

        Log.d(TAG, "=== $title ===")
        dumpNodeRecursive(node, 0, maxDepth)
        Log.d(TAG, "=== $title 结束 ===")
    }

    private fun dumpNodeRecursive(node: AccessibilityNodeInfo, depth: Int, maxDepth: Int) {
        if (depth > maxDepth) return

        val indent = "  ".repeat(depth)
        val text = node.text?.toString()?.take(20) ?: ""
        val className = node.className?.toString()?.substringAfterLast('.') ?: "Unknown"
        val bounds = getBounds(node)
        val packageName = node.packageName?.toString() ?: ""

        Log.d(TAG, "$indent[$depth] $className" +
               (if (text.isNotEmpty()) " text='$text'" else "") +
               " pkg='$packageName' bounds=$bounds children=${node.childCount}")

        // 递归遍历子节点
        for (i in 0 until node.childCount) {
            try {
                node.getChild(i)?.let { child ->
                    dumpNodeRecursive(child, depth + 1, maxDepth)
                }
            } catch (e: Exception) {
                Log.w(TAG, "$indent  [Child node $i failed: ${e.message}]")
            }
        }
    }

    /**
     * 比较两个rootNode是否相同
     */
    private fun compareRootNodes(node1: AccessibilityNodeInfo?, node2: AccessibilityNodeInfo?): Boolean {
        if (node1 == null && node2 == null) return true
        if (node1 == null || node2 == null) return false

        return try {
            val same = node1.packageName == node2.packageName &&
                      node1.className == node2.className &&
                      node1.childCount == node2.childCount &&
                      getBounds(node1) == getBounds(node2)

            Log.d(TAG, "rootNode比较结果: $same")
            if (!same) {
                Log.d(TAG, "  node1: pkg=${node1.packageName}, class=${node1.className}, children=${node1.childCount}")
                Log.d(TAG, "  node2: pkg=${node2.packageName}, class=${node2.className}, children=${node2.childCount}")
            }
            same
        } catch (e: Exception) {
            Log.w(TAG, "比较rootNode时发生异常", e)
            false
        }
    }


}
