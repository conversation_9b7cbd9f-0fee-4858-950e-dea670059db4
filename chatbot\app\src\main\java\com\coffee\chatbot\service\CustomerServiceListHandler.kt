package com.coffee.chatbot.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.graphics.*
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityNodeInfo

/**
 * 客服接待页面处理器 - 纯 Accessibility Services 实现
 *
 * 功能：
 * 1. 检测是否在客服接待页面
 * 2. 查找有未读消息的会话
 * 3. 提取会话信息（昵称、消息、时间）
 * 4. 点击进入会话
 * 5. 提供详细的调试日志
 *
 * 优化：
 * 1. 使用XPath实现精确节点定位，基于节点层级结构分析
 * 2. 直接查找会话列表和会话项，无需多次递归遍历
 * 3. 精确定位会话中的昵称、时间和消息内容元素
 * 4. 优先使用可点击区域执行点击，提高点击成功率
 * 5. 全面管理节点回收，避免内存泄漏
 */

/**
 * 封装会话项的详细信息
 */
data class ConversationDetails(
    val nickname: String? = null,
    val lastMessage: String? = null,
    val timestamp: String? = null,
    val unreadCount: Int = 0,
    val hasWaitingText: Boolean = false
) {
    /**
     * 判断是否有未读消息
     */
    val hasUnread: Boolean
        get() = unreadCount > 0 || hasWaitingText
}

class CustomerServiceListHandler(private val context: Context) {

    companion object {
        private const val TAG = "CustomerServiceHandler"

        // 目标应用包名
        private const val TARGET_PACKAGE = "com.xingin.eva"

        // 点击标记持续时间
        private const val CLICK_MARKER_DURATION = 2000L
    }

    private var accessibilityService: AccessibilityService? = null
    private val handler = Handler(Looper.getMainLooper())
    private var showClickMarker = true
    private var markerView: View? = null
    private val windowManager by lazy { context.getSystemService(Context.WINDOW_SERVICE) as WindowManager }

    // 标记是否已执行过特定用户点击
    private var hasClickedSpecificUser = false

    // 设置 AccessibilityService 实例
    fun setAccessibilityService(service: AccessibilityService) {
        this.accessibilityService = service
        Log.d(TAG, "✅ AccessibilityService 已设置")
    }

    /**
     * 主要入口方法：检查并点击有未读消息的会话
     */
    fun checkAndEnterNewMessage(): Boolean {
        // --- 为实现"仅执行一次点击特定用户"的逻辑 ---
        if (!hasClickedSpecificUser) {
            hasClickedSpecificUser = true // 标记为已执行
            findAndClickByTextOnce("喜八德")
            // 即使没找到也继续正常流程，或者直接返回
            // return true // 如果只想执行这一次点击，可以在这里返回
        }
        // --- 特定用户点击逻辑结束 ---

        Log.d(TAG, "🚀 === 开始检查未读消息 ===")

        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return false
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return false
        }

        // 打印完整的节点层级信息用于调试
        dumpNodeStructure(rootNode, "当前窗口所有节点", 30)

        try {
            // 验证是否在目标应用
            if (!isTargetApp(rootNode)) {
                Log.w(TAG, "❌ 不在目标应用中")
                return false
            }

            // 新逻辑：首先定位到客服接待页面的根容器
            val customerServicePageRoot = findCustomerServicePageRoot(rootNode)
            if (customerServicePageRoot == null) {
                Log.w(TAG, "❌ 未找到客服接待页面容器")
                return false
            }

            Log.d(TAG, "✅ 确认在客服接待页面")

            // 在客服接待页面容器内查找并点击
            val result = findAndClickUnreadConversation(customerServicePageRoot)
            customerServicePageRoot.recycle() // 回收页面根节点
            Log.d(TAG, "🚀 === 检查未读消息完成，结果: $result ===")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "检查未读消息时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 验证是否在目标应用
     */
    private fun isTargetApp(rootNode: AccessibilityNodeInfo): Boolean {
        val packageName = rootNode.packageName?.toString()
        Log.d(TAG, "当前应用包名: $packageName")
        return packageName == TARGET_PACKAGE
    }

    /**
     * 查找客服接待页面的根容器节点
     * 使用XPath直接定位客服接待页面
     */
    private fun findCustomerServicePageRoot(rootNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 根据CustomerList.xml分析，客服接待页在ViewPager的第二个FrameLayout子元素中
        // 使用两种策略查找：1. 先用"客服接待"文本作为锚点 2. 如果失败，使用精确的XPath路径
        
        // 策略1: 使用"客服接待"文本作为锚点定位
        val textXPath = "//TextView[@text='客服接待']"
        Log.d(TAG, "策略1: 使用文本定位客服接待页: $textXPath")
        val customerServiceLabel = rootNode.findNodeByXPath(textXPath)
        
        if (customerServiceLabel != null) {
            // 从文本节点向上查找，直到找到FrameLayout[1]容器
            Log.d(TAG, "✅ 找到'客服接待'文本节点，查找其所在页面根节点")
            
            // 需要向上遍历5层才能找到正确的页面根节点
            var current: AccessibilityNodeInfo? = customerServiceLabel
            var pageRoot: AccessibilityNodeInfo? = null
            
            // 向上遍历到ViewPager的直接子节点FrameLayout
            for (i in 0 until 10) { // 最多向上遍历10层，避免无限循环
                val parent = current?.parent
                if (parent == null) break
                
                if (parent.className?.toString() == "android.widget.FrameLayout" && 
                    parent.parent?.className?.toString() == "androidx.viewpager.widget.ViewPager") {
                    pageRoot = parent
                    Log.d(TAG, "✅ 找到客服接待页面根节点(策略1)")
                    break
                }
                
                // 回收之前的节点，继续向上
                current?.recycle()
                current = parent
            }
            
            // 回收最终未使用的节点
            current?.recycle()
            customerServiceLabel.recycle()
            
            if (pageRoot != null) {
                return pageRoot
            }
        }
        
        // 策略2: 使用精确的XPath路径定位
        val exactXPath = "//ViewPager[0]/FrameLayout[1]"
        Log.d(TAG, "策略2: 使用精确XPath定位客服接待页: $exactXPath")
        val pageRoot = rootNode.findNodeByXPath(exactXPath)
        
        if (pageRoot != null) {
            // 验证找到的页面确实包含"客服接待"文本
            val verifyText = pageRoot.findNodeByXPath("//TextView[@text='客服接待']")
            if (verifyText != null) {
                Log.d(TAG, "✅ 找到客服接待页面根节点(策略2)，并验证包含'客服接待'文本")
                verifyText.recycle()
                return pageRoot
            }
            
            // 不是正确的页面，回收并返回null
            Log.w(TAG, "❌ 找到的页面不包含'客服接待'文本，不是客服接待页")
            pageRoot.recycle()
        } else {
            Log.w(TAG, "❌ 未找到客服接待页面根节点")
        }
        
        return null
    }

    /**
     * 查找并点击有未读消息的会话
     * @param pageRoot 客服接待页面的根节点
     */
    private fun findAndClickUnreadConversation(pageRoot: AccessibilityNodeInfo): Boolean {
        val conversations = findConversationItems(pageRoot)
        Log.d(TAG, "找到 ${conversations.size} 个会话项")

        if (conversations.isEmpty()) {
            Log.w(TAG, "❌ 未找到会话项")
            return false
        }

        try {
            // 遍历每个会话项，查找有未读消息的会话
            for ((index, conversationRow) in conversations.withIndex()) {
                Log.d(TAG, "--- 检查会话 $index ---")

                // 关键步骤：从可能包含多个按钮的完整行中，精确找到包含核心信息的区域
                val mainContentArea = findMainContentArea(conversationRow)
                if (mainContentArea == null) {
                    Log.w(TAG, "  -> 在会话 $index 中未找到主要内容区域，跳过")
                    continue
                }

                try {
                    // 仅对核心区域进行分析，避免"取消收藏"等按钮文本的干扰
                    val details = analyzeConversation(mainContentArea)
                    Log.d(TAG, "  会话详情: 昵称='${details.nickname}', 消息='${details.lastMessage}', 时间='${details.timestamp}', 未读数=${details.unreadCount}")

                    if (details.hasUnread) {
                        Log.d(TAG, "🎯 会话 $index 有未读消息，准备点击")
                        // 点击核心区域
                        return clickConversation(mainContentArea, index)
                    } else {
                        Log.d(TAG, "⭕ 会话 $index 无未读消息")
                    }
                } finally {
                    // 分析和点击完成后，回收核心区域节点
                    mainContentArea.recycle()
                }
            }
        } finally {
            // 确保无论如何都回收所有会话行节点
            conversations.forEach { it.recycle() }
        }

        Log.d(TAG, "❌ 未找到有未读消息的会话")
        return false
    }

    /**
     * 查找所有会话项
     * 使用XPath直接定位会话列表和会话项
     * @param pageRoot 客服接待页面的根节点
     */
    private fun findConversationItems(pageRoot: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        // 使用用户提供的精确XPath路径定位会话列表容器
        val conversationListPath = "//ViewPager[0]/FrameLayout[1]/FrameLayout[0]/FrameLayout[0]/FrameLayout[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/ViewGroup[3]/ScrollView[0]/ViewGroup[0]/*"
        Log.d(TAG, "使用XPath定位会话列表项: $conversationListPath")
        
        // 查找所有会话项的父容器
        val conversationItems = pageRoot.findNodesByXPath(conversationListPath)
        
        if (conversationItems.isEmpty()) {
            Log.w(TAG, "❌ 未找到任何会话项")
            return emptyList()
        }
        
        Log.d(TAG, "✅ 找到 ${conversationItems.size} 个候选会话项，开始过滤")
        
        // 过滤出真正的会话项（包含头像、昵称、消息等信息的ViewGroup）
        // 特征：1. 是ViewGroup 2. 包含可点击区域 3. 包含多个TextView（昵称、时间、消息等）
        val validConversations = conversationItems.filter { node ->
            // 1. 验证是否为ViewGroup
            if (node.className?.toString() != "android.view.ViewGroup") {
                node.recycle()
                return@filter false
            }
            
            // 2. 使用XPath查找包含头像、用户名和消息内容的特征区域
            // 预期每个会话项都有一个内部可点击的ViewGroup，包含用户信息
            val clickableAreas = node.findNodesByXPath("//ViewGroup[@clickable='true']")
            val hasClickable = clickableAreas.isNotEmpty()
            clickableAreas.forEach { it.recycle() }
            
            if (!hasClickable) {
                node.recycle()
                return@filter false
            }
            
            // 3. 验证是否包含足够的文本信息（至少两个TextView，通常是昵称和最后消息）
            val textViews = node.findNodesByXPath("//TextView")
            val hasEnoughText = textViews.size >= 2
            textViews.forEach { it.recycle() }
            
            if (!hasEnoughText) {
                node.recycle()
                return@filter false
            }
            
            // 通过所有验证，这是一个有效的会话项
            true
        }
        
        Log.d(TAG, "✅ 过滤后找到 ${validConversations.size} 个有效会话项")
        return validConversations
    }

    /**
     * 综合分析单个会话节点，提取所有信息
     * 使用XPath精确定位会话中的各个元素
     * @param conversationItem 要分析的会话节点
     * @return 包含所有提取信息的 ConversationDetails 对象
     */
    private fun analyzeConversation(conversationItem: AccessibilityNodeInfo): ConversationDetails {
        Log.d(TAG, "开始分析会话内容，使用XPath精确定位各元素")
        
        // 1. 昵称: 通常是第一个TextView
        val nicknameNode = conversationItem.findNodeByXPath("//ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[0]")
        val nickname = nicknameNode?.text?.toString()
        nicknameNode?.recycle()
        
        // 2. 时间戳: 通常是第二个TextView
        val timestampNode = conversationItem.findNodeByXPath("//ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[1]")
        val timestamp = timestampNode?.text?.toString()
        timestampNode?.recycle()
        
        // 3. 最后消息: 通常是第三个TextView
        val messageNode = conversationItem.findNodeByXPath("//ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[2]")
        val lastMessage = messageNode?.text?.toString()
        messageNode?.recycle()
        
        // 4. 未读消息数: 查找可能存在的未读消息计数标记
        // 这通常是一个独立的TextView，包含数字
        val unreadBadges = conversationItem.findNodesByXPath("//TextView[matches(@text, '^\\d+$')]")
        var unreadCount = 0
        
        if (unreadBadges.isNotEmpty()) {
            // 过滤出真正的未读计数标记（通常尺寸较小，显示为红色背景的角标）
            for (badge in unreadBadges) {
                val text = badge.text?.toString()
                if (text != null && text.matches(Regex("^\\d+$"))) {
                    // 获取节点的边界，检查其大小（未读角标通常很小）
                    val bounds = Rect()
                    badge.getBoundsInScreen(bounds)
                    
                    // 判断是否可能是未读角标（小尺寸）
                    if (bounds.width() < 50 && bounds.height() < 50) {
                        try {
                            unreadCount = text.toInt()
                            Log.d(TAG, "找到未读消息角标: $unreadCount")
                            break
                        } catch (e: NumberFormatException) {
                            Log.w(TAG, "解析未读数量失败: $text")
                        }
                    }
                }
            }
            // 回收所有未读标记节点
            unreadBadges.forEach { it.recycle() }
        }
        
        // 5. 检查是否存在"已等待"文本，这表示有未处理的消息
        val hasWaitingText = lastMessage?.contains("已等待") == true
        
        Log.d(TAG, "会话分析结果: 昵称='$nickname', 时间='$timestamp', 消息='$lastMessage', 未读=$unreadCount, 等待=$hasWaitingText")
        
        return ConversationDetails(nickname, lastMessage, timestamp, unreadCount, hasWaitingText)
    }

    /**
     * 查找未读消息角标
     * 对应XML第84行：<node index="0" text="1" ... class="android.widget.TextView"
     */
    private fun findUnreadMessageBadge(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        return findUnreadMessageBadgeRecursive(node, 0)
    }

    private fun findUnreadMessageBadgeRecursive(node: AccessibilityNodeInfo, depth: Int): AccessibilityNodeInfo? {
        val indent = "  ".repeat(depth)

        // 检查当前节点是否是未读消息角标
        if (node.className?.toString() == "android.widget.TextView") {
            val text = node.text?.toString()
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            Log.d(TAG, "${indent}检查TextView: text='$text', bounds=$bounds, size=${bounds.width()}x${bounds.height()}")

            if (text != null && text.matches(Regex("\\d+"))) {
                Log.d(TAG, "${indent}  -> 文本是数字: $text")

                // 进一步验证：角标通常位置较小且在头像区域
                if (bounds.width() < 50 && bounds.height() < 50) {
                    Log.d(TAG, "${indent}  -> ✅ 尺寸符合角标特征 (${bounds.width()}x${bounds.height()})")
                    Log.d(TAG, "🎯 找到未读消息角标: text='$text', bounds=$bounds")
                    return node
                } else {
                    Log.d(TAG, "${indent}  -> ❌ 尺寸过大，不是角标 (${bounds.width()}x${bounds.height()})")
                }
            } else {
                Log.d(TAG, "${indent}  -> 文本不是纯数字: '$text'")
            }
        } else {
            Log.d(TAG, "${indent}节点类型: ${node.className}, childCount: ${node.childCount}")
        }

        // 递归查找子节点
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                val result = findUnreadMessageBadgeRecursive(child, depth + 1)
                if (result != null) return result
            }
        }

        return null
    }

    /**
     * 点击会话
     * 精确定位会话的可点击区域，避免点击到其他元素
     */
    private fun clickConversation(conversation: AccessibilityNodeInfo, index: Int): Boolean {
        val service = accessibilityService ?: return false
        
        // 查找会话项中最合适的点击区域
        // 优先使用会话项内的可点击ViewGroup，这通常是整个会话的核心可点击部分
        val clickableArea = conversation.findNodeByXPath("//ViewGroup[1]/ViewGroup[0]/ViewGroup[@clickable='true']")
        
        if (clickableArea != null) {
            Log.d(TAG, "✅ 找到会话项的最佳点击区域")
            
            val bounds = Rect()
            clickableArea.getBoundsInScreen(bounds)
            
            // 使用区域中心点作为点击位置
            val centerX = bounds.centerX()
            val centerY = bounds.centerY()
            
            Log.d(TAG, "准备点击会话 $index: bounds=$bounds, center=($centerX, $centerY)")
            
            // 显示点击标记
            showClickMarker(centerX, centerY)
            
            // 先尝试使用node的performAction方法点击
            val clicked = clickableArea.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            
            // 回收点击区域节点
            clickableArea.recycle()
            
            if (clicked) {
                Log.d(TAG, "✅ 使用performAction成功点击会话")
                return true
            }
            
            // 如果performAction失败，使用手势点击
            Log.d(TAG, "performAction点击失败，尝试使用手势点击")
            return performClick(centerX, centerY)
        } else {
            Log.d(TAG, "⚠️ 未找到会话项的最佳点击区域，使用整个会话项的中心点")
            
            // 使用整个会话项的中心点
            val bounds = Rect()
            conversation.getBoundsInScreen(bounds)
            
            val centerX = bounds.centerX()
            val centerY = bounds.centerY()
            
            Log.d(TAG, "准备点击会话 $index: bounds=$bounds, center=($centerX, $centerY)")
            
            // 显示点击标记
            showClickMarker(centerX, centerY)
            
            // 尝试使用node的performAction方法点击
            val clicked = conversation.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            
            if (clicked) {
                Log.d(TAG, "✅ 使用performAction成功点击会话")
                return true
            }
            
            // 如果performAction失败，使用手势点击
            Log.d(TAG, "performAction点击失败，尝试使用手势点击")
            return performClick(centerX, centerY)
        }
    }

    /**
     * 执行点击手势
     */
    private fun performClick(x: Int, y: Int): Boolean {
        val service = accessibilityService ?: return false

        Log.d(TAG, "执行点击手势: ($x, $y)")

        val path = Path().apply {
            moveTo(x.toFloat(), y.toFloat())
        }

        val gestureBuilder = GestureDescription.Builder()
        val strokeDescription = GestureDescription.StrokeDescription(path, 0, 100)
        gestureBuilder.addStroke(strokeDescription)

        val gesture = gestureBuilder.build()

        return service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
            override fun onCompleted(gestureDescription: GestureDescription?) {
                Log.d(TAG, "✅ 点击手势执行完成: ($x, $y)")
            }

            override fun onCancelled(gestureDescription: GestureDescription?) {
                Log.w(TAG, "❌ 点击手势被取消: ($x, $y)")
            }
        }, null)
    }

    /**
     * 详细分析会话项内容
     */
    private fun analyzeConversationItem(conversationItem: AccessibilityNodeInfo, index: Int) {
        // ... existing code ...
    }

    /**
     * 显示全局点击标记
     */
    private fun showClickMarker(x: Int, y: Int) {
        try {
            hideClickMarker() // 先隐藏之前的标记

            // 创建红色圆形标记
            val marker = object : View(context) {
                override fun onDraw(canvas: Canvas) {
                    super.onDraw(canvas)

                    // 红色填充圆
                    val redPaint = Paint().apply {
                        color = Color.RED
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawCircle(25f, 25f, 20f, redPaint)

                    // 白色边框
                    val whitePaint = Paint().apply {
                        color = Color.WHITE
                        style = Paint.Style.STROKE
                        strokeWidth = 4f
                        isAntiAlias = true
                    }
                    canvas.drawCircle(25f, 25f, 20f, whitePaint)
                }
            }

            // 设置悬浮窗参数
            val params = WindowManager.LayoutParams().apply {
                type = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
                format = PixelFormat.TRANSLUCENT
                width = 50
                height = 50
                gravity = Gravity.TOP or Gravity.START
                this.x = x - 25
                this.y = y - 25
            }

            windowManager.addView(marker, params)
            markerView = marker

            Log.d(TAG, "显示点击标记: ($x, $y)")

            // 2秒后自动隐藏
            handler.postDelayed({ hideClickMarker() }, 2000)

        } catch (e: Exception) {
            Log.e(TAG, "显示点击标记失败", e)
        }
    }

    /**
     * 隐藏点击标记
     */
    private fun hideClickMarker() {
        try {
            markerView?.let { view ->
                windowManager.removeView(view)
                markerView = null
                Log.d(TAG, "隐藏点击标记")
            }
        } catch (e: Exception) {
            Log.e(TAG, "隐藏点击标记失败", e)
            markerView = null
        }
    }

    /**
     * 设置是否显示点击标记
     */
    fun setShowClickMarker(show: Boolean) {
        this.showClickMarker = show
        if (!show) {
            hideClickMarker()
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        hideClickMarker()
        handler.removeCallbacksAndMessages(null)
        Log.d(TAG, "资源清理完成")
    }

    // ===== 辅助工具方法 =====

    /**
     * 根据文本查找节点
     */
    private fun findNodesByText(rootNode: AccessibilityNodeInfo, text: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        findNodesByTextRecursive(rootNode, text, result)
        return result
    }

    /**
     * 根据类名查找节点
     */
    private fun findNodesByClassName(rootNode: AccessibilityNodeInfo, className: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        findNodesByClassNameRecursive(rootNode, className, result)
        return result
    }

    private fun findNodesByClassNameRecursive(node: AccessibilityNodeInfo, className: String, result: MutableList<AccessibilityNodeInfo>) {
        if (node.className?.toString() == className) {
            result.add(node)
        }
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findNodesByClassNameRecursive(child, className, result)
                // 注意：不要在这里回收child，因为它可能被添加到result列表中
            }
        }
    }

    private fun findNodesByTextRecursive(node: AccessibilityNodeInfo, text: String, result: MutableList<AccessibilityNodeInfo>) {
        if (node.text?.toString()?.contains(text) == true) {
            result.add(node)
        }
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findNodesByTextRecursive(child, text, result)
                child.recycle()
            }
        }
    }

    /**
     * 递归查找文本
     */
    private fun findTextRecursive(node: AccessibilityNodeInfo, targetText: String): Boolean {
        // 检查当前节点文本
        node.text?.toString()?.let { text ->
            if (text.contains(targetText)) {
                return true
            }
        }

        // 递归检查子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val found = findTextRecursive(child, targetText)
                child.recycle()
                if (found) {
                    return true
                }
            }
        }

        return false
    }

    /**
     * 获取节点边界信息
     */
    private fun getBounds(node: AccessibilityNodeInfo): String {
        val bounds = Rect()
        node.getBoundsInScreen(bounds)
        return "[${bounds.left},${bounds.top}][${bounds.right},${bounds.bottom}]"
    }

    /**
     * 调试点击位置计算
     */
    private fun debugClickPosition(bounds: Rect, clickX: Int, clickY: Int) {
        Log.d(TAG, "=== 点击位置调试信息 ===")
        Log.d(TAG, "目标区域: $bounds")
        Log.d(TAG, "区域尺寸: ${bounds.width()}x${bounds.height()}")
        Log.d(TAG, "区域中心: (${bounds.centerX()}, ${bounds.centerY()})")
        Log.d(TAG, "计算的点击位置: ($clickX, $clickY)")

        // 验证点击位置是否在区域内
        val inBounds = clickX >= bounds.left && clickX <= bounds.right &&
                      clickY >= bounds.top && clickY <= bounds.bottom
        Log.d(TAG, "点击位置是否在区域内: $inBounds")

        if (!inBounds) {
            Log.w(TAG, "❌ 点击位置超出目标区域！")
            Log.w(TAG, "X坐标: $clickX, 应在[${bounds.left}, ${bounds.right}]")
            Log.w(TAG, "Y坐标: $clickY, 应在[${bounds.top}, ${bounds.bottom}]")
        } else {
            Log.d(TAG, "✅ 点击位置在目标区域内")
        }

        // 计算点击位置相对于区域的百分比
        val xPercent = ((clickX - bounds.left).toFloat() / bounds.width() * 100).toInt()
        val yPercent = ((clickY - bounds.top).toFloat() / bounds.height() * 100).toInt()
        Log.d(TAG, "点击位置百分比: X=${xPercent}%, Y=${yPercent}%")
        Log.d(TAG, "=== 调试信息结束 ===")
    }

    /**
     * 转储节点结构用于调试
     */
    private fun dumpNodeStructure(node: AccessibilityNodeInfo?, title: String, maxDepth: Int = 3) {
        if (node == null) {
            Log.d(TAG, "$title: node为null")
            return
        }

        Log.d(TAG, "=== $title ===")
        dumpNodeRecursive(node, 0, maxDepth)
        Log.d(TAG, "=== $title 结束 ===")
    }

    private fun dumpNodeRecursive(node: AccessibilityNodeInfo, depth: Int, maxDepth: Int) {
        if (depth > maxDepth) return

        val indent = "  ".repeat(depth)
        val text = node.text?.toString()?.take(30)?.replace("\n", " ") ?: ""
        val className = node.className?.toString()?.substringAfterLast('.') ?: "Unknown"
        val bounds = getBounds(node)
        val packageName = node.packageName?.toString() ?: ""
        val isClickable = node.isClickable

        Log.d(TAG, "$indent- $className" +
                (if (text.isNotEmpty()) " (text='$text')" else "") +
                (if (isClickable) " [clickable]" else "") +
                " (bounds=$bounds, children=${node.childCount})")

        // 递归遍历子节点
        for (i in 0 until node.childCount) {
            try {
                val child = node.getChild(i)
                if (child != null) {
                    dumpNodeRecursive(child, depth + 1, maxDepth)
                    child.recycle() // 回收子节点，避免内存泄漏
                }
            } catch (e: Exception) {
                Log.w(TAG, "$indent  [Child node $i failed to process: ${e.message}]")
            }
        }
    }

    /**
     * 比较两个rootNode是否相同
     */
    private fun compareRootNodes(node1: AccessibilityNodeInfo?, node2: AccessibilityNodeInfo?): Boolean {
        if (node1 == null && node2 == null) return true
        if (node1 == null || node2 == null) return false

        return try {
            val same = node1.packageName == node2.packageName &&
                      node1.className == node2.className &&
                      node1.childCount == node2.childCount &&
                      getBounds(node1) == getBounds(node2)

            Log.d(TAG, "rootNode比较结果: $same")
            if (!same) {
                Log.d(TAG, "  node1: pkg=${node1.packageName}, class=${node1.className}, children=${node1.childCount}")
                Log.d(TAG, "  node2: pkg=${node2.packageName}, class=${node2.className}, children=${node2.childCount}")
            }
            same
        } catch (e: Exception) {
            Log.w(TAG, "比较rootNode时发生异常", e)
            false
        }
    }

    /**
     * 从一个完整的会话行节点中，找到只包含核心信息（头像、昵称、消息）的那个可点击区域。
     * 这是为了过滤掉侧滑出现的"收藏"、"删除"等按钮文本的干扰，确保提取正确的会话信息。
     */
    private fun findMainContentArea(rowNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 通过XPath精确定位会话项的主要内容区域
        // 通常是一个可点击的ViewGroup，包含头像、昵称和消息内容
        // 路径根据CustomerList.xml分析得出
        val xpath = "//ViewGroup[1]/ViewGroup[0]/ViewGroup[@clickable='true']"
        Log.d(TAG, "查找会话主内容区域: $xpath")
        
        val contentAreas = rowNode.findNodesByXPath(xpath)
        
        if (contentAreas.isEmpty()) {
            Log.w(TAG, "❌ 未找到会话主内容区域")
            return null
        }
        
        // 如果找到多个区域，选择包含最多TextView的那个
        if (contentAreas.size > 1) {
            Log.d(TAG, "找到多个可能的内容区域(${contentAreas.size})，选择最相关的一个")
            
            val mainContent = contentAreas.maxByOrNull { node ->
                val textViews = node.findNodesByXPath("//TextView")
                val count = textViews.size
                textViews.forEach { it.recycle() }
                count
            }
            
            // 回收未使用的节点
            contentAreas.filter { it != mainContent }.forEach { it.recycle() }
            
            return mainContent
        }
        
        // 只有一个区域，直接返回
        return contentAreas[0]
    }

    /**
     * 查找并点击指定文本的会话。
     * 使用XPath精确定位包含指定昵称的会话项
     * @param nickname 要查找的用户昵称
     */
    fun findAndClickByTextOnce(nickname: String) {
        Log.d(TAG, "🚀 === 开始查找并点击昵称为 '$nickname' 的会话 ===")
        
        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return
        }
        
        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return
        }
        
        try {
            // 先找到客服接待页面
            val pageRoot = findCustomerServicePageRoot(rootNode)
            if (pageRoot == null) {
                Log.w(TAG, "❌ 未找到客服接待页面根节点")
                return
            }
            
            // 使用XPath直接定位包含指定昵称的会话项
            // 基于分析的节点结构：查找具有指定文本的TextView，然后找到其所在的会话项
            val conversationXPath = "//ViewGroup[0]/ViewGroup[1]/ViewGroup[3]/ScrollView[0]/ViewGroup[0]/ViewGroup//TextView[@text='$nickname']/../../../.."
            Log.d(TAG, "使用XPath查找包含昵称'$nickname'的会话项: $conversationXPath")
            
            val conversationItem = pageRoot.findNodeByXPath(conversationXPath)
            
            if (conversationItem != null) {
                Log.d(TAG, "✅ 找到包含昵称'$nickname'的会话项")
                
                // 从找到的会话项中定位可点击区域
                val clickableArea = conversationItem.findNodeByXPath("//ViewGroup[@clickable='true']")
                
                if (clickableArea != null) {
                    Log.d(TAG, "✅ 找到会话项的可点击区域")
                    
                    // 执行点击
                    val clicked = clickableArea.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    
                    // 显示点击标记
                    val bounds = Rect()
                    clickableArea.getBoundsInScreen(bounds)
                    showClickMarker(bounds.centerX(), bounds.centerY())
                    
                    // 回收节点
                    clickableArea.recycle()
                    
                    if (clicked) {
                        Log.d(TAG, "✅ 成功点击昵称为'$nickname'的会话")
                    } else {
                        Log.w(TAG, "❌ 点击失败，尝试使用手势点击")
                        val bounds = Rect()
                        conversationItem.getBoundsInScreen(bounds)
                        performClick(bounds.centerX(), bounds.centerY())
                    }
                } else {
                    // 如果找不到可点击区域，尝试点击整个会话项
                    Log.d(TAG, "⚠️ 未找到可点击区域，尝试点击整个会话项")
                    
                    val clicked = conversationItem.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    
                    // 显示点击标记
                    val bounds = Rect()
                    conversationItem.getBoundsInScreen(bounds)
                    showClickMarker(bounds.centerX(), bounds.centerY())
                    
                    if (!clicked) {
                        Log.w(TAG, "❌ 点击失败，尝试使用手势点击")
                        performClick(bounds.centerX(), bounds.centerY())
                    }
                }
                
                // 回收会话项节点
                conversationItem.recycle()
            } else {
                Log.w(TAG, "❌ 未找到昵称为'$nickname'的会话项")
                
                // 备用方案：尝试模糊搜索
                val fuzzyXPath = "//ScrollView[0]/ViewGroup[0]/ViewGroup//TextView[contains(@text, '$nickname')]/../../../.."
                Log.d(TAG, "尝试使用模糊匹配: $fuzzyXPath")
                
                val fuzzyMatch = pageRoot.findNodeByXPath(fuzzyXPath)
                if (fuzzyMatch != null) {
                    Log.d(TAG, "✅ 通过模糊匹配找到可能的会话项")
                    
                    // 执行点击
                    val clicked = fuzzyMatch.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    
                    // 显示点击标记
                    val bounds = Rect()
                    fuzzyMatch.getBoundsInScreen(bounds)
                    showClickMarker(bounds.centerX(), bounds.centerY())
                    
                    if (!clicked) {
                        Log.w(TAG, "❌ 点击失败，尝试使用手势点击")
                        performClick(bounds.centerX(), bounds.centerY())
                    }
                    
                    fuzzyMatch.recycle()
                } else {
                    Log.w(TAG, "❌ 模糊匹配也未找到包含'$nickname'的会话项")
                }
            }
            
            // 回收页面根节点
            pageRoot.recycle()
            
        } catch (e: Exception) {
            Log.e(TAG, "查找并点击指定会话时发生异常", e)
        } finally {
            rootNode.recycle()
        }
    }

}
