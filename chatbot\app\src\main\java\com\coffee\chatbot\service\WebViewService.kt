package com.coffee.chatbot.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import androidx.core.app.NotificationCompat
import com.coffee.chatbot.R
import fi.iki.elonen.NanoHTTPD
import java.io.IOException
import java.net.NetworkInterface
import java.util.concurrent.atomic.AtomicReference

class WebViewService : Service() {

    private var server: NodeHierarchyServer? = null
    private val currentRootNode = AtomicReference<AccessibilityNodeInfo?>(null)

    inner class NodeHierarchyServer : NanoHTTPD(DEFAULT_PORT) {
        override fun serve(session: IHTTPSession): Response {
            val html = generateHtml()
            return newFixedLengthResponse(Response.Status.OK, "text/html; charset=utf-8", html)
        }
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        server = NodeHierarchyServer()
        try {
            server?.start()
            Log.d(TAG, "Web server started on port $DEFAULT_PORT")
        } catch (e: IOException) {
            Log.e(TAG, "Error starting web server", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        server?.stop()
        instance = null
        Log.d(TAG, "WebViewService onDestroy")
    }
    
    fun updateRootNode(rootNode: AccessibilityNodeInfo?) {
        // Create a copy of the node to prevent issues with recycling
        currentRootNode.getAndSet(rootNode?.let { AccessibilityNodeInfo(it) })?.recycle()
    }

    private fun generateHtml(): String {
        val rootNode = currentRootNode.get()
        val sb = StringBuilder()
        sb.append("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Android 节点层级查看器</title>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                    h1 { color: #333; }
                    .action-buttons { display: flex; gap: 10px; margin-bottom: 20px; }
                    .btn { background-color: #007aff; color: white; padding: 10px 15px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; }
                    .btn:hover { background-color: #0056b3; }
                    ul.tree, ul.tree ul { list-style: none; margin: 0; padding-left: 20px; }
                    ul.tree li { margin: 0; padding: 5px 0; position: relative; }
                    .node { border: 1px solid #ddd; padding: 10px; border-radius: 8px; margin: 5px 0; background-color: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
                    .node-info { font-weight: 500; display: flex; justify-content: flex-start; align-items: center; }
                    .node-toggle { cursor: pointer; font-size: 18px; padding: 0 8px; user-select: none; width: 20px; text-align: center; margin-right: 5px; }
                    .node-details { flex: 1; }
                    .node-xpath { color: #e83e8c; font-family: "SF Mono", "Fira Code", "Source Code Pro", monospace; margin-top: 5px; font-size: 13px; margin-left: 25px; }
                    .clickable { background-color: #e6f7ff; }
                    .scrollable { border-left: 4px solid #ffc107; }
                    .focused { border-left: 4px solid #fd7e14; }
                    #nodeXmlModal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
                    .modal-content { position: relative; background-color: #fff; margin: 5% auto; padding: 20px; border-radius: 8px; width: 80%; max-height: 80%; overflow-y: auto; }
                    .close-btn { position: absolute; top: 10px; right: 15px; font-size: 24px; font-weight: bold; cursor: pointer; }
                    .copy-btn { background-color: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; margin-top: 10px; }
                    .copy-btn:hover { background-color: #218838; }
                    #xmlContent { white-space: pre-wrap; background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin-top: 10px; }
                    .search-container { margin-bottom: 20px; display: flex; gap: 10px; }
                    .search-input { flex: 1; padding: 10px; border-radius: 8px; border: 1px solid #ddd; font-size: 16px; }
                    .collapse-all, .expand-all { background-color: #6c757d; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; }
                    .collapse-all:hover, .expand-all:hover { background-color: #5a6268; }
                    .empty-toggle { width: 20px; display: inline-block; margin-right: 5px; }
                </style>
                <script>
                    function refreshPage() { 
                        window.location.reload(); 
                    }
                    
                    function copyXmlToClipboard() {
                        const xmlContent = document.getElementById('xmlContent');
                        const xmlText = xmlContent.textContent;
                        
                        navigator.clipboard.writeText(xmlText).then(() => {
                            alert('XML已复制到剪贴板');
                        }).catch(err => {
                            console.error('复制失败:', err);
                            alert('复制失败，请手动选择并复制');
                        });
                    }
                    
                    function showXmlModal() {
                        document.getElementById('nodeXmlModal').style.display = 'block';
                    }
                    
                    function closeXmlModal() {
                        document.getElementById('nodeXmlModal').style.display = 'none';
                    }
                    
                    // 点击modal外部区域关闭
                    window.onclick = function(event) {
                        const modal = document.getElementById('nodeXmlModal');
                        if (event.target == modal) {
                            closeXmlModal();
                        }
                    }
                    
                    function toggleNode(element) {
                        const nodeContainer = element.closest('li');
                        const childrenContainer = nodeContainer.querySelector('ul');
                        const toggleIcon = element;
                        
                        if (childrenContainer) {
                            if (childrenContainer.style.display === 'none') {
                                childrenContainer.style.display = 'block';
                                toggleIcon.textContent = '−';
                            } else {
                                childrenContainer.style.display = 'none';
                                toggleIcon.textContent = '+';
                            }
                        }
                    }
                    
                    function expandAll() {
                        document.querySelectorAll('.tree ul').forEach(el => {
                            el.style.display = 'block';
                        });
                        document.querySelectorAll('.node-toggle').forEach(el => {
                            el.textContent = '−';
                        });
                    }
                    
                    function collapseAll() {
                        document.querySelectorAll('.tree ul').forEach(el => {
                            el.style.display = 'none';
                        });
                        document.querySelectorAll('.node-toggle').forEach(el => {
                            el.textContent = '+';
                        });
                    }
                    
                    function search() {
                        const searchText = document.getElementById('search-input').value.toLowerCase();
                        const allNodes = document.querySelectorAll('.node');
                        
                        if (!searchText) {
                            allNodes.forEach(node => {
                                node.style.backgroundColor = '';
                                node.classList.remove('highlight');
                            });
                            return;
                        }
                        
                        let foundAny = false;
                        
                        allNodes.forEach(node => {
                            const nodeText = node.textContent.toLowerCase();
                            if (nodeText.includes(searchText)) {
                                node.style.backgroundColor = '#ffffcc';
                                node.classList.add('highlight');
                                
                                // 展开父节点以显示匹配的节点
                                let parent = node.closest('li').parentElement;
                                while (parent) {
                                    if (parent.tagName === 'UL') {
                                        parent.style.display = 'block';
                                        const parentToggle = parent.closest('li')?.querySelector('.node-toggle');
                                        if (parentToggle) {
                                            parentToggle.textContent = '−';
                                        }
                                    }
                                    parent = parent.parentElement;
                                }
                                
                                foundAny = true;
                            } else {
                                node.style.backgroundColor = '';
                                node.classList.remove('highlight');
                            }
                        });
                        
                        if (foundAny) {
                            // 滚动到第一个匹配的节点
                            const firstMatch = document.querySelector('.highlight');
                            if (firstMatch) {
                                firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }
                    }
                    
                    // 监听回车键进行搜索
                    document.addEventListener('DOMContentLoaded', function() {
                        const searchInput = document.getElementById('search-input');
                        searchInput.addEventListener('keypress', function(e) {
                            if (e.key === 'Enter') {
                                search();
                            }
                        });
                    });
                </script>
            </head>
            <body>
                <h1>Android 节点层级查看器</h1>
                <div class="action-buttons">
                    <button class="btn" onclick="refreshPage()">刷新</button>
                    <button class="btn" onclick="showXmlModal()">复制XML</button>
                    <button class="btn expand-all" onclick="expandAll()">全部展开</button>
                    <button class="btn collapse-all" onclick="collapseAll()">全部收起</button>
                </div>
                
                <div class="search-container">
                    <input type="text" id="search-input" class="search-input" placeholder="搜索节点..." />
                    <button class="btn" onclick="search()">搜索</button>
                </div>
                
                <!-- XML模态框 -->
                <div id="nodeXmlModal">
                    <div class="modal-content">
                        <span class="close-btn" onclick="closeXmlModal()">&times;</span>
                        <h2>节点层级XML</h2>
                        <button class="copy-btn" onclick="copyXmlToClipboard()">复制XML</button>
                        <pre id="xmlContent">${generateNodeXml(rootNode)}</pre>
                    </div>
                </div>
        """)

        if (rootNode != null) {
            sb.append("<ul class=\"tree\">")
            renderNodeAsHtml(sb, rootNode, "/")
            sb.append("</ul>")
            // The system will handle recycling of the root node
        } else {
            sb.append("<p>无法获取节点层级，请确保无障碍服务已启用并正在运行。</p>")
        }

        sb.append("""
            <script>
                // 初始状态设置
                document.addEventListener('DOMContentLoaded', function() {
                    // 默认收起除了第一级节点外的所有节点
                    const topLevelNodes = document.querySelectorAll('.tree > li > ul');
                    const otherNodes = document.querySelectorAll('.tree > li > ul ul');
                    
                    otherNodes.forEach(node => {
                        node.style.display = 'none';
                    });
                    
                    // 设置折叠节点的+号
                    document.querySelectorAll('.tree > li > ul ul').forEach(node => {
                        const parentLi = node.closest('li');
                        const toggleBtn = parentLi.querySelector('.node-toggle');
                        if (toggleBtn) {
                            toggleBtn.textContent = '+';
                        }
                    });
                });
            </script>
        """)
        sb.append("</body></html>")
        return sb.toString()
    }

    private fun generateNodeXml(rootNode: AccessibilityNodeInfo?): String {
        if (rootNode == null) return "<!-- 无节点数据 -->"
        
        val sb = StringBuilder()
        sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        appendNodeXml(sb, rootNode, 0)
        return escapeHtml(sb.toString())
    }
    
    private fun appendNodeXml(sb: StringBuilder, node: AccessibilityNodeInfo, indent: Int) {
        val indentStr = "  ".repeat(indent)
        val className = node.className?.toString() ?: "android.view.View"
        
        sb.append("$indentStr<$className")
        
        // 添加属性
        node.text?.let { 
            val escapedText = it.toString().replace("\"", "&quot;") 
            sb.append(" text=\"$escapedText\"")
        }
        node.contentDescription?.let { 
            val escapedDesc = it.toString().replace("\"", "&quot;") 
            sb.append(" content-desc=\"$escapedDesc\"")
        }
        node.viewIdResourceName?.let { sb.append(" resource-id=\"$it\"") }
        
        if (node.isClickable) sb.append(" clickable=\"true\"")
        if (node.isScrollable) sb.append(" scrollable=\"true\"")
        if (node.isFocused) sb.append(" focused=\"true\"")
        
        if (node.childCount > 0) {
            sb.append(">\n")
            
            // 递归添加子节点
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    appendNodeXml(sb, child, indent + 1)
                    child.recycle()
                }
            }
            
            sb.append("$indentStr</$className>\n")
        } else {
            sb.append(" />\n")
        }
    }

    private fun renderNodeAsHtml(sb: StringBuilder, node: AccessibilityNodeInfo, xpath: String) {
        val className = node.className?.toString()?.substringAfterLast('.') ?: "Unknown"
        val childXpath = "$xpath$className[${getChildIndex(node)}]"
        val hasChildren = node.childCount > 0
        
        sb.append("<li><div class=\"node ${if (node.isClickable) "clickable" else ""} ${if (node.isScrollable) "scrollable" else ""} ${if (node.isFocused) "focused" else ""}\">")
        sb.append("<div class=\"node-info\">")
        if (hasChildren) {
            sb.append("<span class=\"node-toggle\" onclick=\"toggleNode(this)\">−</span>")
        } else {
            sb.append("<span class=\"empty-toggle\"></span>")
        }
        sb.append("<div class=\"node-details\">${escapeHtml(buildNodeDescription(node))}</div>")
        sb.append("</div>")
        sb.append("<div class=\"node-xpath\">XPath: ${escapeHtml(childXpath)}</div>")
        
        if (hasChildren) {
            sb.append("<ul>")
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    renderNodeAsHtml(sb, child, "$childXpath/")
                    child.recycle() // Recycle child node after use
                }
            }
            sb.append("</ul>")
        }
        sb.append("</div></li>")
    }

    private fun buildNodeDescription(node: AccessibilityNodeInfo): String {
        val sb = StringBuilder(node.className?.toString()?.substringAfterLast('.') ?: "Unknown")
        node.text?.let { sb.append(" - \"$it\"") }
        node.contentDescription?.let { sb.append(" [desc: \"$it\"]") }
        node.viewIdResourceName?.let { sb.append(" [id: $it]") }
        return sb.toString()
    }

    private fun getChildIndex(node: AccessibilityNodeInfo): Int {
        val parent = node.parent ?: return 0
        var index = 0
        for (i in 0 until parent.childCount) {
            val child = parent.getChild(i)
            if (child != null) {
                if (child.className == node.className) {
                    if (child == node) {
                        parent.recycle()
                        return index
                    }
                    index++
                }
                child.recycle()
            }
        }
        parent.recycle()
        return 0
    }
    
    private fun escapeHtml(text: String): String {
        return text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID, "Web View Service", NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Shows a persistent notification while the web server is active"
            }
            getSystemService(NotificationManager::class.java).createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val ipAddress = getLocalIpAddress()
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Web 服务已启动")
            .setContentText("访问 http://$ipAddress:$DEFAULT_PORT 查看节点层级")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    override fun onBind(intent: Intent?): IBinder? = null
    
    companion object {
        private const val TAG = "WebViewService"
        private const val NOTIFICATION_ID = 2
        private const val CHANNEL_ID = "WebViewServiceChannel"
        private const val DEFAULT_PORT = 8080
        
        private var instance: WebViewService? = null
        fun getInstance(): WebViewService? = instance
        
        fun getLocalIpAddress(): String {
            try {
                NetworkInterface.getNetworkInterfaces().asSequence().forEach { networkInterface ->
                    networkInterface.inetAddresses.asSequence().forEach { inetAddress ->
                        if (!inetAddress.isLoopbackAddress && inetAddress.hostAddress.indexOf(':') < 0) {
                            return inetAddress.hostAddress
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting IP address", e)
            }
            return "127.0.0.1"
        }
    }
} 