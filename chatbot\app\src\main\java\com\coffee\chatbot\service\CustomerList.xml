<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy rotation="0">
    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2400]">
        <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2352]">
            <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2352]">
                <node index="0" text="" resource-id="com.xingin.eva:id/action_bar_root" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2352]">
                    <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2352]">
                        <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2352]">
                            <node index="0" text="" resource-id="com.xingin.eva:id/homeViewPager" class="androidx.viewpager.widget.ViewPager" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                <node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                    <node index="0" text="" resource-id="com.xingin.eva:id/rootView" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                        <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                            <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                    <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                        <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                            <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                                    <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                                        <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]" />
                                                                        <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                                            <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2199]">
                                                                                    <node index="1" text="" resource-id="" class="android.widget.HorizontalScrollView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,132][1080,264]">
                                                                                        <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,132][432,264]">
                                                                                            <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,132][264,264]">
                                                                                                <node index="0" text="客服接待" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[36,168][228,228]" />
                                                                                                <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[39,240][212,246]" /></node>
                                                                                            <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[264,132][432,264]">
                                                                                                <node index="0" text="通知" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[300,168][396,228]" />
                                                                                                <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[357,144][432,189]">
                                                                                                    <node index="0" text="99+" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[357,146][432,187]" /></node>
                                                                                            </node>
                                                                                        </node>
                                                                                    </node>
                                                                                    <node index="2" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,264][1080,2199]">
                                                                                        <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,264][1080,396]">
                                                                                            <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,307][209,353]">
                                                                                                <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[46,307][92,353]" />
                                                                                                <node index="1" text="在线" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[98,307][168,353]" />
                                                                                                <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[174,313][209,348]" /></node>
                                                                                            <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[226,315][227,345]" />
                                                                                            <node index="2" text="排队数" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[256,307][361,353]" />
                                                                                            <node index="3" text="0" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[367,304][425,356]" />
                                                                                            <node index="4" text="今日接待" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[460,307][600,353]" />
                                                                                            <node index="5" text="0" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[606,304][664,356]" />
                                                                                            <node index="6" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[744,301][802,359]" />
                                                                                            <node index="7" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[860,301][918,359]" />
                                                                                            <node index="8" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[976,301][1034,359]" />
                                                                                            <node index="9" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1011,301][1034,324]" /></node>
                                                                                        <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,396][1080,528]">
                                                                                            <node index="0" text="当前会话" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[24,405][276,519]" />
                                                                                            <node index="1" text="全部会话" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[276,405][528,519]" />
                                                                                            <node index="2" text="收藏会话" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[528,405][780,519]" /></node>
                                                                                        <node index="2" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,528][1080,636]">
                                                                                            <node index="0" text="" resource-id="" class="android.widget.ScrollView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[36,528][1044,636]">
                                                                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[36,528][1044,636]">
                                                                                                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[36,561][78,603]" />
                                                                                                    <node index="1" text="当前设置消息提醒不会响铃" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[102,528][838,636]" />
                                                                                                    <node index="2" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[886,545][1044,620]">
                                                                                                        <node index="0" text="去开启" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[911,558][1019,607]" /></node>
                                                                                                </node>
                                                                                            </node>
                                                                                        </node>
                                                                                        <node index="3" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,2199]">
                                                                                            <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[540,642][540,582]" />
                                                                                            <node index="1" text="" resource-id="" class="android.widget.ScrollView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,2199]">
                                                                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,1074]">
                                                                                                    <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,858]">
                                                                                                        <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,858]">
                                                                                                            <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,858]">
                                                                                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][225,858]" />
                                                                                                                <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[855,642][1080,858]">
                                                                                                                    <node index="0" text="收藏" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[926,722][1010,779]" /></node>
                                                                                                            </node>
                                                                                                        </node>
                                                                                                        <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,858]">
                                                                                                            <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,858]">
                                                                                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,642][1080,858]">
                                                                                                                    <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,687][174,813]">
                                                                                                                        <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[49,688][174,813]" />
                                                                                                                        <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[49,688][174,813]" />
                                                                                                                        <node index="2" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[155,687][174,730]">
                                                                                                                            <node index="0" text="1" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[155,687][174,730]" /></node>
                                                                                                                    </node>
                                                                                                                    <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[219,642][1080,858]">
                                                                                                                        <node index="0" text="喜八德" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[219,687][354,738]" />
                                                                                                                        <node index="1" text="刚刚" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[969,693][1035,732]" />
                                                                                                                        <node index="2" text="你好啊老板" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[219,762][848,810]" />
                                                                                                                        <node index="3" text="已等待1秒" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[884,768][1035,807]" /></node>
                                                                                                                </node>
                                                                                                            </node>
                                                                                                        </node>
                                                                                                    </node>
                                                                                                    <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,858][1080,1074]">
                                                                                                        <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,858][1080,1074]">
                                                                                                            <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,858][1080,1074]">
                                                                                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,858][225,1074]" />
                                                                                                                <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[855,858][1080,1074]">
                                                                                                                    <node index="0" text="收藏" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[926,938][1010,995]" /></node>
                                                                                                            </node>
                                                                                                        </node>
                                                                                                        <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,858][1080,1074]">
                                                                                                            <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,858][1080,1074]">
                                                                                                                <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,858][1080,1074]">
                                                                                                                    <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,903][174,1029]">
                                                                                                                        <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[49,904][174,1029]" />
                                                                                                                        <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[49,904][174,1029]" /></node>
                                                                                                                    <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[219,858][1080,1074]">
                                                                                                                        <node index="0" text="小红薯6625A7E6" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[219,903][559,954]" />
                                                                                                                        <node index="1" text="2025-05-04 11:58" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[773,909][1035,948]" />
                                                                                                                        <node index="2" text="会话长时间无新消息，系统关闭会话" resource-id="" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[219,978][1044,1026]" /></node>
                                                                                                                </node>
                                                                                                            </node>
                                                                                                        </node>
                                                                                                    </node>
                                                                                                </node>
                                                                                            </node>
                                                                                        </node>
                                                                                    </node>
                                                                                </node>
                                                                            </node>
                                                                        </node>
                                                                    </node>
                                                                </node>
                                                            </node>
                                                            <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,168]" /></node>
                                                    </node>
                                                </node>
                                            </node>
                                        </node>
                                    </node>
                                </node>
                            </node>
                            <node index="1" text="" resource-id="" class="android.view.View" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2199][1080,2202]" />
                            <node index="2" text="" resource-id="com.xingin.eva:id/mTabLayout" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2202][1080,2352]">
                                <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2202][1080,2352]">
                                    <node index="0" text="" resource-id="com.xingin.eva:id/mHomeTabView" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2202][270,2352]">
                                        <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2202][270,2352]">
                                            <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[40,2202][229,2352]">
                                                <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[97,2219][172,2335]">
                                                    <node index="0" text="" resource-id="com.xingin.eva:id/mIcon" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[97,2219][172,2291]" />
                                                    <node index="1" text="首页" resource-id="com.xingin.eva:id/mTitle" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[104,2294][164,2335]" /></node>
                                            </node>
                                        </node>
                                    </node>
                                    <node index="1" text="" resource-id="com.xingin.eva:id/mMessageTabView" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[270,2202][540,2352]">
                                        <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[270,2202][540,2352]">
                                            <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[310,2202][499,2352]">
                                                <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[367,2219][442,2335]">
                                                    <node index="0" text="" resource-id="com.xingin.eva:id/mIcon" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[367,2219][442,2291]" />
                                                    <node index="1" text="消息" resource-id="com.xingin.eva:id/mTitle" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[374,2294][434,2335]" /></node>
                                                <node index="1" text="99+" resource-id="com.xingin.eva:id/mUnReadNum" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[415,2205][495,2259]" /></node>
                                        </node>
                                    </node>
                                    <node index="2" text="" resource-id="com.xingin.eva:id/mDataTabView" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[540,2202][810,2352]">
                                        <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[540,2202][810,2352]">
                                            <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[580,2202][769,2352]">
                                                <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[637,2219][712,2335]">
                                                    <node index="0" text="" resource-id="com.xingin.eva:id/mIcon" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[637,2219][712,2291]" />
                                                    <node index="1" text="数据" resource-id="com.xingin.eva:id/mTitle" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[644,2294][704,2335]" /></node>
                                            </node>
                                        </node>
                                    </node>
                                    <node index="3" text="" resource-id="com.xingin.eva:id/mMeTabView" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[810,2202][1080,2352]">
                                        <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[810,2202][1080,2352]">
                                            <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[850,2202][1039,2352]">
                                                <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[907,2219][982,2335]">
                                                    <node index="0" text="" resource-id="com.xingin.eva:id/mIcon" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[907,2219][982,2291]" />
                                                    <node index="1" text="我的" resource-id="com.xingin.eva:id/mTitle" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[914,2294][974,2335]" /></node>
                                            </node>
                                        </node>
                                    </node>
                                </node>
                            </node>
                        </node>
                        <node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1476][182,1872]">
                            <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1476][182,1872]">
                                <node index="0" text="" resource-id="com.xingin.eva:id/view_expand" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1476][182,1872]">
                                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[28,1507][154,1841]">
                                        <node index="0" text="" resource-id="com.xingin.eva:id/v_customer_service" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[28,1513][154,1650]">
                                            <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[61,1531][121,1591]">
                                                <node index="0" text="" resource-id="com.xingin.eva:id/icon" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[61,1531][121,1591]" /></node>
                                            <node index="1" text="客服" resource-id="com.xingin.eva:id/tv_customer_service_title" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[61,1591][121,1632]" /></node>
                                        <node index="1" text="" resource-id="com.xingin.eva:id/v_line_second" class="android.view.View" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[70,1650][112,1653]" />
                                        <node index="2" text="" resource-id="com.xingin.eva:id/v_i_want_feedback" class="android.widget.LinearLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[28,1653][154,1790]">
                                            <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[61,1671][121,1731]" />
                                            <node index="1" text="反馈" resource-id="com.xingin.eva:id/tv_i_want_feedback_title" class="android.widget.TextView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[61,1731][121,1772]" /></node>
                                        <node index="3" text="" resource-id="com.xingin.eva:id/btn_collapse" class="android.widget.FrameLayout" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[28,1790][154,1841]">
                                            <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[79,1790][103,1817]" /></node>
                                    </node>
                                </node>
                            </node>
                        </node>
                    </node>
                </node>
            </node>
        </node>
        <node index="1" text="" resource-id="android:id/navigationBarBackground" class="android.view.View" package="com.xingin.eva" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2352][1080,2400]" /></node>
</hierarchy>